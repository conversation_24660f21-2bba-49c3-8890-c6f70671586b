import {
  <PERSON><PERSON><PERSON>,
  MiddlewareConsumer,
  RequestMethod,
  NestModule,
} from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ThrottlerModule } from "@nestjs/throttler";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { ScheduleModule } from "@nestjs/schedule";
import { RequestLoggerMiddleware } from "./common/middleware/request-logger.middleware";
import { LoggerModule } from "./common/logger/logger.module";
import { CommonServicesModule } from "./common/services/common-services.module";

// Core modules
import { AuthModule } from "./auth/auth.module";
import { UsersModule } from "./users/users.module";
import { JobsModule } from "./jobs/jobs.module";
import { ApplicationsModule } from "./applications/applications.module";
import { RatingsModule } from "./ratings/ratings.module";
import { PayoutsModule } from "./payouts/payouts.module";
import { PaymentsModule } from "./payments/payments.module";
import { DisputesModule } from "./disputes/disputes.module";
import { NotificationsModule } from "./notifications/notifications.module";
import { ChatModule } from "./chat/chat.module";
import { GamificationModule } from "./gamification/gamification.module";
import { AnalyticsModule } from "./analytics/analytics.module";
import { OtpModule } from "./otp/otp.module";
import { JobTemplatesModule } from "./job-templates/job-templates.module";
import { SettingsModule } from "./settings/settings.module";
import { ReportsModule } from "./reports/reports.module";
import { ActivityLogModule } from "./activity-log/activity-log.module";
import { FavoritesModule } from "./favorites/favorites.module";
import { DocumentsModule } from "./documents/documents.module";
import { CompaniesModule } from "./companies/companies.module";
import { MigrationModule } from "./database/migrations/migration.module";
import { AdminModule } from "./admin/admin.module";
import { HealthModule } from "./health/health.module";

// Entities
import { UserBadge } from "./gamification/entities/user-badge.entity";
import { JobTemplate } from "./job-templates/entities/job-template.entity";
import { User } from "./users/entities/user.entity";
import { Job } from "./jobs/entities/job.entity";
import { Application } from "./applications/entities/application.entity";
import { Rating } from "./ratings/entities/rating.entity";
import { Payout } from "./payouts/entities/payout.entity";
import { Dispute } from "./disputes/entities/dispute.entity";
import { GenericChat } from "./chat/entities/generic-chat.entity";
import { Chat } from "./chat/entities/chat.entity";
import { ActivityLog } from "./activity-log/entities/activity-log.entity";
import { ChatMessage } from "./chat/entities/chat-message.entity";
import { ChatParticipant } from "./chat/entities/chat-participant.entity";
import { Badge } from "./gamification/entities/badge.entity";
import { Otp } from "./otp/entities/otp.entity";
import { Setting } from "./settings/entities/setting.entity";
import { TrustScoreLog } from "./users/entities/trust-score-log.entity";
import { Document } from "./users/entities/document.entity";
import { Favorite } from "./favorites/entities/favorite.entity";
import { Report } from "./reports/entities/report.entity";
import { Notification } from "./notifications/entities/notification.entity";
import { PasswordResetToken } from "./auth/entities/password-reset-token.entity";
import { Company } from "./companies/entities/company.entity";
import { Migration } from "./database/migrations/migration.entity";
import { EscrowAccount } from "./payments/entities/escrow.entity";

@Module({
  imports: [
    // Core configuration
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    LoggerModule,
    CommonServicesModule,
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: "postgres",
        url:
          configService.get("DATABASE_URL") ||
          "postgresql://localhost:5432/jobplatform",
        entities: [
          User,
          Job,
          Application,
          Rating,
          Payout,
          Dispute,
          Notification,
          GenericChat,
          Chat,
          ChatMessage,
          ChatParticipant,
          Badge,
          UserBadge,
          JobTemplate,
          Setting,
          Report,
          ActivityLog,
          Otp,
          Favorite,
          TrustScoreLog,
          Document,
          PasswordResetToken,
          Company,
          Migration,
          EscrowAccount,
        ],
        synchronize: configService.get("NODE_ENV") !== "production", // Only in development
        ssl: configService.get("NODE_ENV") === "production",
      }),
    }),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get<number>("THROTTLE_TTL", 60),
        limit: configService.get<number>("THROTTLE_LIMIT", 100),
      }),
    }),
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),

    // Feature modules
    AuthModule,
    UsersModule,
    JobsModule,
    ApplicationsModule,
    RatingsModule,
    PayoutsModule,
    PaymentsModule,
    DisputesModule,
    NotificationsModule,
    ChatModule,
    GamificationModule,
    AnalyticsModule,
    OtpModule,
    JobTemplatesModule,
    SettingsModule,
    ReportsModule,
    ActivityLogModule,
    FavoritesModule,
    DocumentsModule,
    CompaniesModule,
    MigrationModule,
    AdminModule,
    HealthModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply the RequestLoggerMiddleware to all routes
    consumer
      .apply(RequestLoggerMiddleware)
      .forRoutes({ path: "*", method: RequestMethod.ALL });
  }
}
