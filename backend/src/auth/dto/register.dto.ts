import {
  Is<PERSON>mail,
  IsNotEmpty,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>idate<PERSON>f,
} from "class-validator";

export class RegisterDto {
  @IsString()
  @IsNotEmpty()
  name!: string;

  @IsEmail()
  @IsOptional()
  @ValidateIf((o) => !o.phone)
  email?: string;

  @IsString()
  @IsOptional()
  @ValidateIf((o) => !o.email)
  phone?: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password!: string;

  // @IsEnum(UserRole)
  // @IsNotEmpty()
  // role: UserRole;

  // @IsString()
  // @IsOptional()
  // @ValidateIf((o) => o.role === UserRole.COMPANY)
  // companyName?: string;
}
