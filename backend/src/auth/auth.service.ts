import { Injectable, Inject, Logger } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { UsersService } from "../users/users.service";
import { OtpService } from "../otp/otp.service";
import {
  OtpRequestDto,
  OtpVerifyDto,
  LoginDto,
  RegisterDto,
} from "./dto/auth.dto";
import { ForgotPasswordDto } from "./dto/forgot-password.dto";
import {
  ResetPasswordDto,
  ValidateResetTokenDto,
} from "./dto/reset-password.dto";
import crypto from "crypto";
import { UserRole } from "@shared/types";
import { User } from "../users/entities/user.entity";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { PasswordResetToken } from "./entities/password-reset-token.entity";
import { NotificationsService } from "../notifications/notifications.service";
import { EmailService } from "../common/services/email/email.service";
import { AuthUtils } from "./utils/auth.utils";
import { <PERSON>rror<PERSON>andler } from "../common/utils/error-handler.util";

/**
 * Auth response interface
 */
export interface AuthResponse {
  access_token: string;
  token: string;
  refreshToken: string;
  user: Omit<User, "password">;
  isNewUser?: boolean;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @Inject(UsersService)
    private readonly usersService: UsersService,
    @Inject(JwtService)
    private readonly jwtService: JwtService,
    @Inject(OtpService)
    private readonly otpService: OtpService,
    @InjectRepository(PasswordResetToken)
    private readonly passwordResetTokenRepository: Repository<PasswordResetToken>,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService,
    @Inject(EmailService)
    private readonly emailService: EmailService
  ) {}

  /**
   * Validate user credentials
   * @param email User email
   * @param password User password
   * @returns User without password or null if invalid
   */
  async validateUser(
    email: string,
    password: string
  ): Promise<Omit<User, "password"> | null> {
    const user = await this.usersService.findByEmail(email, true);
    if (
      user?.password &&
      (await AuthUtils.comparePasswords(password, user.password))
    ) {
      return AuthUtils.excludePassword(user);
    }
    return null;
  }

  /**
   * Login user with email/phone and password
   * @param loginDto Login credentials
   * @returns Auth response with tokens and user
   */
  async login(loginDto: LoginDto): Promise<AuthResponse> {
    try {
      let user: User | null = null;

      if (loginDto.email) {
        const foundUser = await this.usersService.findByEmail(
          loginDto.email,
          true
        );
        if (
          foundUser?.password &&
          (await AuthUtils.comparePasswords(
            loginDto.password,
            foundUser.password
          ))
        ) {
          user = foundUser;
        }
      } else if (loginDto.phone) {
        const foundUser = await this.usersService.findByPhone(
          loginDto.phone,
          true
        );
        if (
          foundUser?.password &&
          (await AuthUtils.comparePasswords(
            loginDto.password,
            foundUser.password
          ))
        ) {
          user = foundUser;
        }
      }

      if (!user) {
        return ErrorHandler.handleUnauthorized("Invalid credentials");
      }

      // Check if user is banned
      if (user.isBanned) {
        return ErrorHandler.handleForbidden("Your account has been banned");
      }

      // Check if user is active
      if (!user.isActive) {
        return ErrorHandler.handleForbidden("Your account is inactive");
      }

      return AuthUtils.createAuthResponse(user, this.jwtService);
    } catch (error) {
      this.logger.error(`Error during login: ${(error as Error).message}`);
      throw ErrorHandler.handleError(error, "Authentication");
    }
  }

  /**
   * Register a new user
   * @param registerDto Registration data
   * @returns Auth response with tokens and user
   */
  private async register(
    registerDto: RegisterDto,
    role: UserRole = UserRole.WORKER
  ): Promise<AuthResponse> {
    try {
      // Create user
      const user = await this.usersService.create({
        ...registerDto,
        role,
      });

      // Send welcome email if email is provided
      if (user.email) {
        try {
          //TODO: need to handle depending upon the role
          const emailSent = await this.emailService.sendWelcomeEmail(
            user.email,
            user.name
          );

          if (emailSent) {
            this.logger.log(`Welcome email sent to ${user.email}`);
          } else {
            this.logger.warn(`Failed to send welcome email to ${user.email}`);
          }
        } catch (error) {
          this.logger.error(
            `Error sending welcome email: ${(error as Error).message}`
          );
        }
      }

      return AuthUtils.createAuthResponse(user, this.jwtService);
    } catch (error) {
      this.logger.error(
        `Error during registration: ${(error as Error).message}`
      );
      throw ErrorHandler.handleError(error, "User Registration");
    }
  }

  async registerWorker(registerDto: RegisterDto): Promise<AuthResponse> {
    return this.register(registerDto, UserRole.WORKER);
  }

  async registerCompany(registerDto: RegisterDto): Promise<AuthResponse> {
    return this.register(registerDto, UserRole.COMPANY);
  }

  //TODO: need to use only otp based registration and login for workers
  /**
   * Request OTP for phone number
   * @param otpRequestDto OTP request data
   * @returns OTP response
   */
  async requestOtp(otpRequestDto: OtpRequestDto): Promise<{ message: string }> {
    return this.otpService.generateAndSendOtp(otpRequestDto.phone);
  }

  /**
   * Verify OTP and login/register user
   * @param otpVerifyDto OTP verification data
   * @returns Auth response with tokens and user
   */
  async verifyOtp(otpVerifyDto: OtpVerifyDto): Promise<AuthResponse> {
    try {
      const isValid = await this.otpService.verifyOtp(
        otpVerifyDto.phone,
        otpVerifyDto.code
      );

      if (!isValid) {
        return ErrorHandler.handleUnauthorized("Invalid OTP");
      }

      // Check if user exists
      let user = await this.usersService.findByPhone(otpVerifyDto.phone);
      let isNewUser = false;

      // If user doesn't exist, create a new one
      if (!user) {
        // Generate a random password
        const randomPassword = Math.random().toString(36).slice(-8);
        const hashedPassword = await AuthUtils.hashPassword(randomPassword);

        user = await this.usersService.create({
          phone: otpVerifyDto.phone,
          name: "New User",
          role: UserRole.WORKER,
          password: hashedPassword,
        });
        isNewUser = true;
      }

      return AuthUtils.createAuthResponse(user, this.jwtService, isNewUser);
    } catch (error) {
      this.logger.error(`Error verifying OTP: ${(error as Error).message}`);
      throw ErrorHandler.handleError(error, "OTP Verification");
    }
  }

  /**
   * Request password reset for a user
   * @param forgotPasswordDto Email or phone to send reset instructions
   * @returns Success message
   */
  async forgotPassword(
    forgotPasswordDto: ForgotPasswordDto
  ): Promise<{ message: string }> {
    try {
      const { email, phone } = forgotPasswordDto;

      // Validate that either email or phone is provided
      if (!email && !phone) {
        return ErrorHandler.handleBadRequest(
          "Either email or phone is required"
        );
      }

      // Find user by email or phone
      let user: User | null = null;
      if (email) {
        user = await this.usersService.findByEmail(email);
      } else if (phone) {
        user = await this.usersService.findByPhone(phone);
      }

      // For security reasons, don't reveal if the user exists or not
      if (!user) {
        return {
          message:
            "If an account exists with this email/phone, password reset instructions will be sent",
        };
      }

      // Generate a random token
      const token = crypto.randomBytes(32).toString("hex");

      // Set expiry time (1 hour)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);

      // Save token to database
      await this.passwordResetTokenRepository.save({
        userId: user.id,
        email: user.email || "",
        phone: user.phone || "",
        token,
        expiresAt,
        used: false,
      });

      // Send reset instructions based on the contact method
      if (email) {
        // Send email with reset link
        try {
          const emailSent = await this.emailService.sendPasswordResetEmail(
            email,
            token,
            user.name
          );

          if (emailSent) {
            this.logger.log(`Password reset email sent to ${email}`);
          } else {
            this.logger.warn(`Failed to send password reset email to ${email}`);
          }
        } catch (error) {
          this.logger.error(
            `Error sending password reset email: ${(error as Error).message}`
          );
        }

        // Send notification to user
        await this.notificationsService.create({
          userId: user.id,
          title: "Password Reset Requested",
          message:
            "You have requested a password reset. Check your email for instructions.",
          type: "alert",
          sendPush: true,
        });
      } else if (phone) {
        // Send SMS with OTP
        try {
          await this.otpService.generateAndSendOtp(phone);
          this.logger.log(`Password reset OTP sent to ${phone}`);
        } catch (error) {
          this.logger.error(
            `Error sending password reset OTP: ${(error as Error).message}`
          );
        }

        // Send notification to user
        await this.notificationsService.create({
          userId: user.id,
          title: "Password Reset Requested",
          message:
            "You have requested a password reset. Check your phone for the OTP.",
          type: "alert",
          sendPush: true,
        });
      }

      return { message: "Password reset instructions sent successfully" };
    } catch (error) {
      this.logger.error(
        `Error in forgot password flow: ${(error as Error).message}`
      );
      throw ErrorHandler.handleError(error, "Password Reset");
    }
  }

  /**
   * Validate a password reset token
   * @param validateResetTokenDto Token to validate
   * @returns Whether the token is valid
   */
  async validateResetToken(
    validateResetTokenDto: ValidateResetTokenDto
  ): Promise<{ valid: boolean; message: string }> {
    const { token } = validateResetTokenDto;

    const resetToken = await this.passwordResetTokenRepository.findOne({
      where: {
        token,
        used: false,
      },
    });

    if (!resetToken) {
      return { valid: false, message: "Invalid or expired token" };
    }

    // Check if token is expired
    if (new Date() > resetToken.expiresAt) {
      return { valid: false, message: "Token has expired" };
    }

    return { valid: true, message: "Token is valid" };
  }

  /**
   * Reset user password with a valid token
   * @param resetPasswordDto Token and new password
   * @returns Success message
   */
  async resetPassword(
    resetPasswordDto: ResetPasswordDto
  ): Promise<{ message: string }> {
    try {
      const { token, password } = resetPasswordDto;

      // Validate token
      const tokenValidation = await this.validateResetToken({ token });
      if (!tokenValidation.valid) {
        return ErrorHandler.handleUnauthorized(tokenValidation.message);
      }

      // Get token from database
      const resetToken = await this.passwordResetTokenRepository.findOne({
        where: {
          token,
          used: false,
        },
      });

      if (!resetToken) {
        return ErrorHandler.handleUnauthorized("Invalid or expired token");
      }

      // Get user
      const user = await this.usersService.findOne(resetToken.userId);

      // Hash new password
      const hashedPassword = await AuthUtils.hashPassword(password);

      // Update user password
      await this.usersService.update(user.id, {
        password: hashedPassword,
      });

      // Mark token as used
      resetToken.used = true;
      await this.passwordResetTokenRepository.save(resetToken);

      // Send email confirmation if user has email
      if (user.email) {
        try {
          const emailSent =
            await this.emailService.sendPasswordResetSuccessEmail(
              user.email,
              user.name
            );

          if (emailSent) {
            this.logger.log(
              `Password reset confirmation email sent to ${user.email}`
            );
          } else {
            this.logger.warn(
              `Failed to send password reset confirmation email to ${user.email}`
            );
          }
        } catch (error) {
          this.logger.error(
            `Error sending password reset confirmation email: ${
              (error as Error).message
            }`
          );
        }
      }

      // Send notification to user
      await this.notificationsService.create({
        userId: user.id,
        title: "Password Reset Successful",
        message: "Your password has been reset successfully.",
        type: "alert",
        sendPush: true,
      });

      return { message: "Password reset successful" };
    } catch (error) {
      this.logger.error(
        `Error resetting password: ${(error as Error).message}`
      );
      throw ErrorHandler.handleError(error, "Password Reset");
    }
  }
}
