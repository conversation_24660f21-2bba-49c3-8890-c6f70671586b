import { Test, TestingModule } from "@nestjs/testing";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { BadRequestException } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { UsersService } from "../users/users.service";
import { OtpService } from "../otp/otp.service";
import { SanitizationService } from "../common/services/sanitization.service";
import { UserRole } from "../common/enums/user-role.enum";
import bcrypt from "bcrypt";

jest.mock("bcrypt");

describe("AuthService", () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;

  let sanitizationService: jest.Mocked<SanitizationService>;

  const mockUser = {
    id: "1",
    email: "<EMAIL>",
    phone: "+1234567890",
    password: "hashedPassword",
    name: "Test User",
    role: UserRole.WORKER,
    trustScore: 100,
    isKycVerified: false,
    isActive: true,
    isBanned: false,
    profilePicture: null,
    dateOfBirth: null,
    address: null,
    city: null,
    state: null,
    country: null,
    postalCode: null,
    bio: null,
    skills: "communication,teamwork",
    education: null,
    experience: null,
    languages: "english,spanish",
    upiId: null,
    deviceTokens: [],
    emailVerified: true,
    phoneVerified: false,
    lastLoginAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    worker: null,
    documents: [],
    trustScoreLogs: [],
    payouts: [],
    notifications: [],
    activityLogs: [],
    companies: [],
    chatsAsWorker: [],
    chatsAsCompany: [],
    applications: [],
    favorites: [],
    raisedDisputes: [],
    receivedDisputes: [],
    givenRatings: [],
    receivedRatings: [],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findByEmail: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
          },
        },
        {
          provide: OtpService,
          useValue: {
            generateOtp: jest.fn(),
            verifyOtp: jest.fn(),
          },
        },
        {
          provide: SanitizationService,
          useValue: {
            sanitizeEmail: jest.fn(),
            sanitizeText: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    jwtService = module.get(JwtService);

    sanitizationService = module.get(SanitizationService);
  });

  describe("validateUser", () => {
    it("should return user data when credentials are valid", async () => {
      const email = "<EMAIL>";
      const password = "password123";

      usersService.findByEmail.mockResolvedValue(mockUser as any); //TODO: fix it
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);

      const result = await service.validateUser(email, password);

      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
      });
      expect(usersService.findByEmail).toHaveBeenCalledWith(email);
      expect(bcrypt.compare).toHaveBeenCalledWith(password, mockUser.password);
    });

    it("should return null when user is not found", async () => {
      usersService.findByEmail.mockResolvedValue(null);

      const result = await service.validateUser("<EMAIL>", "password");

      expect(result).toBeNull();
    });

    it("should return null when password is invalid", async () => {
      usersService.findByEmail.mockResolvedValue(mockUser as any); //TODO: fix this
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      const result = await service.validateUser(
        "<EMAIL>",
        "wrongpassword"
      );

      expect(result).toBeNull();
    });

    it("should return null when user is not active", async () => {
      const inactiveUser = { ...mockUser, isActive: false };
      usersService.findByEmail.mockResolvedValue(inactiveUser as any); //TODO: fix this
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);

      const result = await service.validateUser("<EMAIL>", "password");

      expect(result).toBeNull();
    });
  });

  describe("login", () => {
    it("should return access token for valid user", async () => {
      const user = {
        id: "1",
        email: "<EMAIL>",
        password: "password123",
        role: UserRole.WORKER,
      };
      const token = "jwt-token";

      jwtService.sign.mockReturnValue(token);

      const result = await service.login(user);

      expect(result).toEqual({
        access_token: token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
        },
      });
      expect(jwtService.sign).toHaveBeenCalledWith({
        email: user.email,
        sub: user.id,
        role: user.role,
      });
    });
  });

  describe("register", () => {
    const registerDto = {
      email: "<EMAIL>",
      password: "password123",
      role: UserRole.WORKER,
      name: "John Doe",
    };

    it("should create new user successfully", async () => {
      const hashedPassword = "hashedPassword";
      const newUser = { ...mockUser, ...registerDto, password: hashedPassword };

      usersService.findByEmail.mockResolvedValue(null);
      sanitizationService.sanitizeEmail.mockReturnValue(registerDto.email);
      sanitizationService.sanitizeText.mockReturnValue(registerDto.name);
      (bcrypt.hash as jest.Mock).mockResolvedValue(hashedPassword);
      usersService.create.mockResolvedValue(newUser as any); //TODO: fix this
      jwtService.sign.mockReturnValue("jwt-token");

      const result = await service.registerWorker(registerDto);

      expect(result).toHaveProperty("access_token");
      expect(result).toHaveProperty("user");
      expect(usersService.create).toHaveBeenCalledWith({
        ...registerDto,
        password: hashedPassword,
      });
    });

    it("should throw BadRequestException when user already exists", async () => {
      usersService.findByEmail.mockResolvedValue(mockUser as any); //TODO: fix this

      await expect(service.registerWorker(registerDto)).rejects.toThrow(
        BadRequestException
      );
    });
  });
});
