import { Inject, Injectable } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { ConfigService } from "@nestjs/config";
import { UserRole } from "../../common/enums/user-role.enum";

/**
 * JWT payload interface
 */
export interface JwtPayload {
  sub: string;
  email?: string;
  phone?: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

/**
 * User from JWT payload
 */
export interface JwtUser {
  id: string;
  email?: string;
  phone?: string;
  role: UserRole;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(@Inject(ConfigService) configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>("JWT_SECRET", "secret"),
    });
  }

  async validate(payload: JwtPayload): Promise<JwtUser> {
    return {
      id: payload.sub,
      email: payload.email || "",
      phone: payload.phone || "",
      role: payload.role,
    };
  }
}
