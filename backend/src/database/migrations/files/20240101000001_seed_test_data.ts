import { QueryRunner } from "typeorm";
import bcrypt from "bcrypt";

/**
 * Migration to seed comprehensive test data aligned with current entity structure
 */
export const up = async (queryRunner: QueryRunner): Promise<void> => {
  console.log("Starting comprehensive test data seeding...");

  // Hash passwords for test users
  const adminPassword = await bcrypt.hash("admin123", 10);
  const companyPassword = await bcrypt.hash("company123", 10);
  const workerPassword = await bcrypt.hash("worker123", 10);

  // ==================== ADMIN USERS ====================
  console.log("Creating admin users...");

  // Insert admin users (5 admins with different roles)
  const adminIds: string[] = [];
  const adminData = [
    {
      username: "admin_super",
      email: "<EMAIL>",
      phone: "+1234567890",
      name: "<PERSON> <PERSON><PERSON>",
      role: "super_admin",
      bio: "Platform super administrator with full system access.",
    },
    {
      username: "admin_main",
      email: "<EMAIL>",
      phone: "+1234567891",
      name: "Main Admin",
      role: "admin",
      bio: "Main platform administrator handling day-to-day operations.",
    },
    {
      username: "admin_support",
      email: "<EMAIL>",
      phone: "+1234567892",
      name: "Support Admin",
      role: "admin",
      bio: "Customer support administrator handling user issues.",
    },
    {
      username: "admin_content",
      email: "<EMAIL>",
      phone: "+1234567893",
      name: "Content Moderator",
      role: "admin",
      bio: "Content moderation administrator managing platform content.",
    },
    {
      username: "admin_finance",
      email: "<EMAIL>",
      phone: "+1234567894",
      name: "Finance Admin",
      role: "admin",
      bio: "Finance administrator handling payments and disputes.",
    },
  ];

  for (let i = 0; i < adminData.length; i++) {
    const admin = adminData[i];
    const result = await queryRunner.query(`
      INSERT INTO users (
        id, username, email, phone, password, name, role,
        "trustScore", "isKycVerified", "isActive", "emailVerified", "phoneVerified",
        bio, "profilePicture", "lastLoginAt"
      ) VALUES (
        uuid_generate_v4(), '${admin.username}', '${admin.email}', '${
      admin.phone
    }',
        '${adminPassword}', '${admin.name}', '${admin.role}',
        100, TRUE, TRUE, TRUE, TRUE,
        '${admin.bio}', 'https://randomuser.me/api/portraits/men/${20 + i}.jpg',
        NOW() - INTERVAL '${Math.floor(Math.random() * 7)} days'
      ) RETURNING id;
    `);
    adminIds.push(result[0].id);
  }

  // ==================== COMPANY USERS ====================
  console.log("Creating company users...");

  // Company data with more realistic information
  const companyData = [
    {
      username: "techsolutions_inc",
      email: "<EMAIL>",
      phone: "+**********",
      name: "TechSolutions Inc.",
      industry: "Technology",
      size: "51-200",
      foundedYear: 2018,
      website: "https://techsolutions.com",
      description:
        "Leading provider of innovative technology solutions for businesses.",
      city: "San Francisco",
      state: "CA",
      registrationNumber: "TS2018001",
      taxId: "12-3456789",
    },
    {
      username: "greenleaf_landscaping",
      email: "<EMAIL>",
      phone: "+**********",
      name: "GreenLeaf Landscaping",
      industry: "Landscaping",
      size: "11-50",
      foundedYear: 2020,
      website: "https://greenleaf-landscaping.com",
      description:
        "Professional landscaping services for residential and commercial properties.",
      city: "Austin",
      state: "TX",
      registrationNumber: "GL2020001",
      taxId: "23-4567890",
    },
    {
      username: "quickserve_restaurants",
      email: "<EMAIL>",
      phone: "+**********",
      name: "QuickServe Restaurants",
      industry: "Food Service",
      size: "201-500",
      foundedYear: 2015,
      website: "https://quickserve.com",
      description: "Fast-casual restaurant chain serving fresh, quality meals.",
      city: "Chicago",
      state: "IL",
      registrationNumber: "QS2015001",
      taxId: "34-5678901",
    },
    {
      username: "buildright_construction",
      email: "<EMAIL>",
      phone: "+1555000004",
      name: "BuildRight Construction",
      industry: "Construction",
      size: "101-500",
      foundedYear: 2017,
      website: "https://buildright.com",
      description:
        "Full-service construction company specializing in residential and commercial projects.",
      city: "Denver",
      state: "CO",
      registrationNumber: "BR2017001",
      taxId: "45-6789012",
    },
    {
      username: "datadrive_analytics",
      email: "<EMAIL>",
      phone: "+**********",
      name: "DataDrive Analytics",
      industry: "Data Analytics",
      size: "11-50",
      foundedYear: 2021,
      website: "https://datadrive.com",
      description:
        "Advanced data analytics and business intelligence solutions.",
      city: "Seattle",
      state: "WA",
      registrationNumber: "DD2021001",
      taxId: "56-7890123",
    },
    {
      username: "healthplus_medical",
      email: "<EMAIL>",
      phone: "+**********",
      name: "HealthPlus Medical Services",
      industry: "Healthcare",
      size: "51-200",
      foundedYear: 2019,
      website: "https://healthplus.com",
      description:
        "Comprehensive healthcare services and medical staffing solutions.",
      city: "Miami",
      state: "FL",
      registrationNumber: "HP2019001",
      taxId: "67-8901234",
    },
    {
      username: "edusmart_learning",
      email: "<EMAIL>",
      phone: "+**********",
      name: "EduSmart Learning Center",
      industry: "Education",
      size: "11-50",
      foundedYear: 2020,
      website: "https://edusmart.com",
      description: "Innovative educational programs and tutoring services.",
      city: "Boston",
      state: "MA",
      registrationNumber: "ES2020001",
      taxId: "78-9012345",
    },
    {
      username: "fashionforward_retail",
      email: "<EMAIL>",
      phone: "+**********",
      name: "FashionForward Retail",
      industry: "Retail",
      size: "201-500",
      foundedYear: 2016,
      website: "https://fashionforward.com",
      description: "Trendy fashion retail chain with locations nationwide.",
      city: "Los Angeles",
      state: "CA",
      registrationNumber: "FF2016001",
      taxId: "89-0123456",
    },
    {
      username: "logitech_transport",
      email: "<EMAIL>",
      phone: "+1555000009",
      name: "LogiTech Transport",
      industry: "Logistics",
      size: "101-500",
      foundedYear: 2018,
      website: "https://logitech-transport.com",
      description:
        "Reliable logistics and transportation services for businesses.",
      city: "Atlanta",
      state: "GA",
      registrationNumber: "LT2018001",
      taxId: "90-1234567",
    },
    {
      username: "creativeminds_design",
      email: "<EMAIL>",
      phone: "+1555000010",
      name: "CreativeMinds Design",
      industry: "Design",
      size: "1-10",
      foundedYear: 2022,
      website: "https://creativeminds.com",
      description:
        "Creative design agency specializing in branding and digital experiences.",
      city: "New York",
      state: "NY",
      registrationNumber: "CM2022001",
      taxId: "01-2345678",
    },
  ];

  // Insert company users (10 companies)
  const companyUserIds: string[] = [];

  for (let i = 0; i < companyData.length; i++) {
    const company = companyData[i];
    const trustScore = 70 + Math.floor(Math.random() * 30); // 70-99 trust score
    const isVerified = i < 8; // First 8 companies are verified

    // Create company user
    const companyUserResult = await queryRunner.query(`
      INSERT INTO users (
        id, username, email, phone, password, name, role,
        "trustScore", "isKycVerified", "isActive", "emailVerified", "phoneVerified",
        bio, "profilePicture", "lastLoginAt"
      ) VALUES (
        uuid_generate_v4(), '${company.username}', '${company.email}', '${
      company.phone
    }',
        '${companyPassword}', '${company.name}', 'company',
        ${trustScore}, ${isVerified}, TRUE, TRUE, ${isVerified},
        '${company.description}', 'https://example.com/logos/${
      company.username
    }.png',
        NOW() - INTERVAL '${Math.floor(Math.random() * 30)} days'
      ) RETURNING id;
    `);

    const companyUserId = companyUserResult[0].id;
    companyUserIds.push(companyUserId);

    // Create company profile
    await queryRunner.query(`
      INSERT INTO companies (
        id, "userId", "registrationNumber", "taxId", website, size, industry, "foundedYear"
      ) VALUES (
        uuid_generate_v4(), '${companyUserId}', '${company.registrationNumber}', '${company.taxId}',
        '${company.website}', '${company.size}', '${company.industry}', ${company.foundedYear}
      );
    `);

    // Add company documents for verification
    if (isVerified) {
      await queryRunner.query(`
        INSERT INTO documents (
          id, "userId", "documentType", "documentUrl", "documentNumber", "verificationStatus",
          "verifiedBy", "verifiedAt"
        ) VALUES (
          uuid_generate_v4(), '${companyUserId}', 'company_registration',
          'https://example.com/documents/registration_${i}.pdf', '${
        company.registrationNumber
      }',
          'verified', '${adminIds[0]}', NOW() - INTERVAL '${Math.floor(
        Math.random() * 60
      )} days'
        );
      `);

      await queryRunner.query(`
        INSERT INTO documents (
          id, "userId", "documentType", "documentUrl", "documentNumber", "verificationStatus",
          "verifiedBy", "verifiedAt"
        ) VALUES (
          uuid_generate_v4(), '${companyUserId}', 'tax_document',
          'https://example.com/documents/tax_${i}.pdf', '${company.taxId}',
          'verified', '${adminIds[0]}', NOW() - INTERVAL '${Math.floor(
        Math.random() * 60
      )} days'
        );
      `);
    } else {
      // Pending verification documents
      await queryRunner.query(`
        INSERT INTO documents (
          id, "userId", "documentType", "documentUrl", "documentNumber", "verificationStatus"
        ) VALUES (
          uuid_generate_v4(), '${companyUserId}', 'company_registration',
          'https://example.com/documents/registration_${i}.pdf', '${company.registrationNumber}',
          'pending'
        );
      `);
    }
  }

  // ==================== WORKER USERS ====================
  console.log("Creating worker users...");

  // Worker data with more realistic profiles
  const workerData = [
    {
      firstName: "John",
      lastName: "Smith",
      city: "New York",
      state: "NY",
      skills: "Data Entry, Customer Service",
    },
    {
      firstName: "Emma",
      lastName: "Johnson",
      city: "Los Angeles",
      state: "CA",
      skills: "Marketing, Social Media",
    },
    {
      firstName: "Michael",
      lastName: "Williams",
      city: "Chicago",
      state: "IL",
      skills: "Construction, Manual Labor",
    },
    {
      firstName: "Sophia",
      lastName: "Jones",
      city: "Houston",
      state: "TX",
      skills: "Administrative, Organization",
    },
    {
      firstName: "David",
      lastName: "Brown",
      city: "Phoenix",
      state: "AZ",
      skills: "Delivery, Driving",
    },
    {
      firstName: "Olivia",
      lastName: "Davis",
      city: "Philadelphia",
      state: "PA",
      skills: "Event Planning, Coordination",
    },
    {
      firstName: "James",
      lastName: "Miller",
      city: "San Antonio",
      state: "TX",
      skills: "Security, Safety",
    },
    {
      firstName: "Ava",
      lastName: "Wilson",
      city: "San Diego",
      state: "CA",
      skills: "Cleaning, Maintenance",
    },
    {
      firstName: "Robert",
      lastName: "Moore",
      city: "Dallas",
      state: "TX",
      skills: "Landscaping, Gardening",
    },
    {
      firstName: "Isabella",
      lastName: "Taylor",
      city: "San Jose",
      state: "CA",
      skills: "Food Service, Hospitality",
    },
    {
      firstName: "William",
      lastName: "Anderson",
      city: "Austin",
      state: "TX",
      skills: "Photography, Creative",
    },
    {
      firstName: "Mia",
      lastName: "Thomas",
      city: "Jacksonville",
      state: "FL",
      skills: "Retail, Sales",
    },
    {
      firstName: "Joseph",
      lastName: "Jackson",
      city: "Fort Worth",
      state: "TX",
      skills: "Warehouse, Logistics",
    },
    {
      firstName: "Charlotte",
      lastName: "White",
      city: "Columbus",
      state: "OH",
      skills: "Healthcare Support, Care",
    },
    {
      firstName: "Daniel",
      lastName: "Harris",
      city: "Charlotte",
      state: "NC",
      skills: "IT Support, Technical",
    },
    {
      firstName: "Amelia",
      lastName: "Martin",
      city: "Indianapolis",
      state: "IN",
      skills: "Education, Tutoring",
    },
    {
      firstName: "Matthew",
      lastName: "Thompson",
      city: "San Francisco",
      state: "CA",
      skills: "Software Development, Programming",
    },
    {
      firstName: "Harper",
      lastName: "Garcia",
      city: "Seattle",
      state: "WA",
      skills: "Design, Creative Services",
    },
    {
      firstName: "Andrew",
      lastName: "Martinez",
      city: "Denver",
      state: "CO",
      skills: "Fitness, Personal Training",
    },
    {
      firstName: "Evelyn",
      lastName: "Robinson",
      city: "Boston",
      state: "MA",
      skills: "Research, Analysis",
    },
  ];

  // Insert worker users (20 workers)
  const workerIds: string[] = [];

  for (let i = 0; i < workerData.length; i++) {
    const worker = workerData[i];
    const username = `${worker.firstName.toLowerCase()}_${worker.lastName.toLowerCase()}`;
    const email = `${worker.firstName.toLowerCase()}.${worker.lastName.toLowerCase()}@email.com`;
    const phone = `+1${6000000 + i}`;
    const fullName = `${worker.firstName} ${worker.lastName}`;
    const trustScore = 50 + Math.floor(Math.random() * 50); // Random score between 50-99
    const isVerified = i < 15; // First 15 workers are verified

    const result = await queryRunner.query(`
      INSERT INTO users (
        id, username, email, phone, password, name, role,
        "trustScore", "isKycVerified", "isActive", "emailVerified", "phoneVerified",
        bio, "profilePicture", "lastLoginAt", "upiId"
      ) VALUES (
        uuid_generate_v4(), '${username}', '${email}', '${phone}',
        '${workerPassword}', '${fullName}', 'worker',
        ${trustScore}, ${isVerified}, TRUE, TRUE, ${isVerified},
        'Experienced worker specializing in ${
          worker.skills
        }. Looking for flexible job opportunities.',
        'https://randomuser.me/api/portraits/${i % 2 === 0 ? "men" : "women"}/${
      i + 1
    }.jpg',
        NOW() - INTERVAL '${Math.floor(Math.random() * 7)} days',
        '${worker.firstName.toLowerCase()}${worker.lastName.toLowerCase()}@upi'
      ) RETURNING id;
    `);

    workerIds.push(result[0].id);

    // Add worker metadata
    const workerMetadata = {
      skills: worker.skills.split(", "),
      experience: Math.floor(Math.random() * 10) + 1, // 1-10 years experience
      availability: ["weekdays", "weekends", "evenings"][
        Math.floor(Math.random() * 3)
      ],
      preferredJobTypes: ["part-time", "full-time", "contract"][
        Math.floor(Math.random() * 3)
      ],
      location: {
        city: worker.city,
        state: worker.state,
        country: "USA",
        radius: Math.floor(Math.random() * 50) + 10, // 10-60 km radius
      },
    };

    await queryRunner.query(`
      UPDATE users SET metadata = '${JSON.stringify(workerMetadata)}'
      WHERE id = '${result[0].id}';
    `);

    // Add worker documents for verification
    if (isVerified) {
      await queryRunner.query(`
        INSERT INTO documents (
          id, "userId", "documentType", "documentUrl", "documentNumber", "verificationStatus",
          "verifiedBy", "verifiedAt"
        ) VALUES (
          uuid_generate_v4(), '${result[0].id}', 'id_proof',
          'https://example.com/documents/id_${i}.pdf', 'ID${30000 + i}',
          'verified', '${adminIds[0]}', NOW() - INTERVAL '${Math.floor(
        Math.random() * 90
      )} days'
        );
      `);

      if (i < 12) {
        await queryRunner.query(`
          INSERT INTO documents (
            id, "userId", "documentType", "documentUrl", "documentNumber", "verificationStatus",
            "verifiedBy", "verifiedAt"
          ) VALUES (
            uuid_generate_v4(), '${result[0].id}', 'address_proof',
            'https://example.com/documents/address_${i}.pdf', 'AP${40000 + i}',
            'verified', '${adminIds[0]}', NOW() - INTERVAL '${Math.floor(
          Math.random() * 90
        )} days'
          );
        `);
      }
    } else {
      // Pending verification documents
      await queryRunner.query(`
        INSERT INTO documents (
          id, "userId", "documentType", "documentUrl", "documentNumber", "verificationStatus"
        ) VALUES (
          uuid_generate_v4(), '${result[0].id}', 'id_proof',
          'https://example.com/documents/id_${i}.pdf', 'ID${30000 + i}',
          'pending'
        );
      `);
    }

    // Add trust score logs for verified workers
    if (isVerified && i < 12) {
      const previousScore = Math.max(50, trustScore - 10);
      await queryRunner.query(`
        INSERT INTO trust_score_logs (
          id, "userId", "previousScore", "newScore", change, reason, "relatedEntityType", "adminId"
        ) VALUES (
          uuid_generate_v4(), '${
            result[0].id
          }', ${previousScore}, ${trustScore}, ${trustScore - previousScore},
          'Completed verification process successfully', 'verification', '${
            adminIds[0]
          }'
        );
      `);
    }
  }

  // ==================== JOBS ====================
  console.log("Creating jobs...");

  // Job data with more detailed information
  const jobData = [
    {
      title: "Full Stack Developer",
      description:
        "We need an experienced full stack developer for a 3-month project. Must have experience with React, Node.js, and PostgreSQL.",
      category: "Technology",
      payRate: 45,
      payRateType: "hourly",
      estimatedHours: 160,
      trustScoreRequired: 80,
      requiresLaptop: true,
      requiresSmartphone: false,
      isEmergencyJob: false,
    },
    {
      title: "Digital Marketing Specialist",
      description:
        "Looking for a marketing specialist to help with social media campaigns, content creation, and SEO optimization.",
      category: "Marketing",
      payRate: 25,
      payRateType: "hourly",
      estimatedHours: 80,
      trustScoreRequired: 60,
      requiresLaptop: true,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
    {
      title: "Data Entry Clerk",
      description:
        "Data entry specialist needed for a 2-week project. Fast typing speed and attention to detail required.",
      category: "Administrative",
      payRate: 18,
      payRateType: "hourly",
      estimatedHours: 40,
      trustScoreRequired: 50,
      requiresLaptop: true,
      requiresSmartphone: false,
      isEmergencyJob: false,
    },
    {
      title: "Customer Support Representative",
      description:
        "Customer service representative needed to handle incoming calls and support customers via chat and email.",
      category: "Customer Service",
      payRate: 20,
      payRateType: "hourly",
      estimatedHours: 120,
      trustScoreRequired: 65,
      requiresLaptop: true,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
    {
      title: "Warehouse Associate",
      description:
        "Warehouse worker needed for inventory management, order fulfillment, and shipping operations.",
      category: "Warehouse",
      payRate: 16,
      payRateType: "hourly",
      estimatedHours: 160,
      trustScoreRequired: 55,
      requiresLaptop: false,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
    {
      title: "Delivery Driver",
      description:
        "Delivery driver needed for local deliveries. Must have valid driver's license and clean driving record.",
      category: "Delivery",
      payRate: 22,
      payRateType: "hourly",
      estimatedHours: 100,
      trustScoreRequired: 70,
      requiresLaptop: false,
      requiresSmartphone: true,
      isEmergencyJob: true,
    },
    {
      title: "Event Coordinator",
      description:
        "Event staff needed for upcoming corporate event. Professional appearance and excellent communication skills required.",
      category: "Events",
      payRate: 30,
      payRateType: "hourly",
      estimatedHours: 24,
      trustScoreRequired: 75,
      requiresLaptop: false,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
    {
      title: "Administrative Assistant",
      description:
        "Administrative assistant needed for general office duties, scheduling, and document management.",
      category: "Administrative",
      payRate: 19,
      payRateType: "hourly",
      estimatedHours: 80,
      trustScoreRequired: 60,
      requiresLaptop: true,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
    {
      title: "Sales Associate",
      description:
        "Sales associate needed for retail location. Experience in customer service preferred but not required.",
      category: "Retail",
      payRate: 17,
      payRateType: "hourly",
      estimatedHours: 120,
      trustScoreRequired: 55,
      requiresLaptop: false,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
    {
      title: "Retail Specialist",
      description:
        "Retail associate needed for busy shopping season. Flexible hours available, weekend work required.",
      category: "Retail",
      payRate: 16,
      payRateType: "hourly",
      estimatedHours: 100,
      trustScoreRequired: 50,
      requiresLaptop: false,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
  ];

  // Create jobs (2 per company, 20 total)
  const jobIds: string[] = [];

  for (let i = 0; i < companyUserIds.length; i++) {
    const companyUserId = companyUserIds[i];
    const companyInfo = companyData[i];

    // Create 2 jobs per company
    for (let j = 0; j < 2; j++) {
      const jobIndex = i * 2 + j;
      if (jobIndex >= jobData.length) break;

      const job = jobData[jobIndex];

      // Job details with some randomization
      const payRate = job.payRate + Math.floor(Math.random() * 10) - 5; // ±$5 variation
      const estimatedHours =
        job.estimatedHours + Math.floor(Math.random() * 20) - 10; // ±10 hours variation
      const maxPositions = 1 + Math.floor(Math.random() * 3); // 1-3 positions

      // Dates
      const startDate = new Date();
      startDate.setDate(
        startDate.getDate() + 1 + Math.floor(Math.random() * 14)
      ); // Start 1-14 days from now

      const endDate = new Date(startDate);
      endDate.setHours(endDate.getHours() + estimatedHours); // End after estimated hours

      // Status distribution
      let status = "open";
      let filledPositions = 0;

      if (jobIndex < 2) {
        status = "in_progress";
        filledPositions = 1;
      } else if (jobIndex >= 18) {
        status = "completed";
        filledPositions = maxPositions;
      } else if (jobIndex >= 16) {
        status = "cancelled";
      }

      // Location based on company location
      const location = `${companyInfo.city}, ${companyInfo.state}`;
      const latitude = 40.7128 + (Math.random() - 0.5) * 10; // Rough US coordinates
      const longitude = -74.006 + (Math.random() - 0.5) * 20;

      const result = await queryRunner.query(`
        INSERT INTO jobs (
          id, "companyId", title, description, location,
          city, state, country, "postalCode", latitude, longitude,
          "startDateTime", "endDateTime",
          "payRate", "estimatedHours", "payRateType",
          "trustScoreRequired", "isEmergencyJob",
          "requiresLaptop", "requiresSmartphone",
          status, "maxPositions", "filledPositions"
        ) VALUES (
          uuid_generate_v4(), '${companyUserId}', '${job.title}', '${
        job.description
      }', '${location}',
          '${companyInfo.city}', '${companyInfo.state}', 'USA', '${
        10000 + jobIndex
      }', ${latitude}, ${longitude},
          '${startDate.toISOString()}', '${endDate.toISOString()}',
          ${payRate}, ${estimatedHours}, '${job.payRateType}',
          ${job.trustScoreRequired}, ${job.isEmergencyJob},
          ${job.requiresLaptop}, ${job.requiresSmartphone},
          '${status}', ${maxPositions}, ${filledPositions}
        ) RETURNING id;
      `);

      jobIds.push(result[0].id);
    }
  }

  // ==================== APPLICATIONS ====================
  console.log("Creating applications...");

  // Application cover letters with more variety
  const coverLetters = [
    "I am very interested in this position and believe my skills align perfectly with your requirements. I have relevant experience and am available to start immediately.",
    "I would like to apply for this role as it matches my career goals and expertise. I am flexible with scheduling and committed to delivering quality work.",
    "I have extensive experience in this field and would be a great fit for this role. My background includes similar projects and I have excellent references.",
    "I am excited about this opportunity and believe my skills match your requirements perfectly. I am detail-oriented and work well both independently and in teams.",
    "I am available immediately and have all the qualifications listed in the job description. I am eager to contribute to your team's success.",
    "I have been looking for exactly this type of position and am confident I can exceed your expectations. My work ethic and dedication set me apart.",
    "My background in this industry makes me an ideal candidate for this job. I understand the challenges and have the skills to overcome them.",
    "I am highly motivated and would appreciate the opportunity to work with your company. I am committed to professional growth and excellence.",
    "I have the necessary experience and am available for all the listed shifts. I am reliable, punctual, and take pride in my work quality.",
    "I am reliable, punctual, and have all the skills required for this position. I look forward to discussing how I can contribute to your team.",
  ];

  // Create applications (each worker applies to 2-4 jobs)
  const applicationIds: string[] = [];

  for (let i = 0; i < workerIds.length; i++) {
    const workerId = workerIds[i];

    // Determine how many applications this worker will submit (2-4)
    const numApplications = 2 + Math.floor(Math.random() * 3);

    // Create a set to track which jobs this worker has already applied to
    const appliedJobs = new Set();

    for (let j = 0; j < numApplications; j++) {
      // Find a job this worker hasn't applied to yet
      let jobIndex: number;
      do {
        jobIndex = Math.floor(Math.random() * jobIds.length);
      } while (appliedJobs.has(jobIndex));

      appliedJobs.add(jobIndex);
      const jobId = jobIds[jobIndex];

      // Determine application status based on various factors
      let status = "pending";
      let completedAt: string | null = null;
      let cancelledAt: string | null = null;
      let rejectionReason: string | null = null;

      // First 2 jobs are in_progress, so some applications should be accepted
      if (jobIndex < 2 && i < 3) {
        status = "accepted";
      }
      // Jobs with completed status should have completed applications
      else if (jobIndex >= 18) {
        if (Math.random() < 0.8) {
          status = "completed";
          completedAt =
            "NOW() - INTERVAL '" + Math.floor(Math.random() * 30) + " days'";
        } else {
          status = "no_show";
        }
      }
      // Some applications should be rejected
      else if (Math.random() < 0.15) {
        status = "rejected";
        rejectionReason = "Experience requirements not met";
      }
      // Some applications should be withdrawn
      else if (Math.random() < 0.1) {
        status = "withdrawn";
        cancelledAt =
          "NOW() - INTERVAL '" + Math.floor(Math.random() * 7) + " days'";
      }

      // Select a random cover letter
      const coverLetterIndex = Math.floor(Math.random() * coverLetters.length);

      const result = await queryRunner.query(`
        INSERT INTO applications (
          id, "jobId", "workerId", status, "coverLetter",
          "rejectionReason", "cancelledAt", "completedAt",
          "createdAt", "updatedAt"
        ) VALUES (
          uuid_generate_v4(), '${jobId}', '${workerId}', '${status}',
          '${coverLetters[coverLetterIndex]}',
          ${rejectionReason ? `'${rejectionReason}'` : "NULL"},
          ${cancelledAt || "NULL"},
          ${completedAt || "NULL"},
          NOW() - INTERVAL '${Math.floor(Math.random() * 21)} days',
          NOW() - INTERVAL '${Math.floor(Math.random() * 7)} days'
        ) RETURNING id;
      `);

      applicationIds.push(result[0].id);
    }
  }

  // ==================== RATINGS ====================
  console.log("Creating ratings...");

  // Create ratings for completed applications
  for (let i = 0; i < applicationIds.length; i++) {
    // Only create ratings for some applications (about 60%)
    if (Math.random() < 0.6) {
      // Get application details
      const applicationResult = await queryRunner.query(`
        SELECT a.id, a.status, a."workerId", j."companyId", j.id as "jobId", c."userId" as "companyUserId"
        FROM applications a
        JOIN jobs j ON a."jobId" = j.id
        JOIN companies c ON j."companyId" = c.id
        WHERE a.id = '${applicationIds[i]}'
      `);

      if (applicationResult.length === 0) continue;

      const application = applicationResult[0];

      // Only create ratings for completed applications
      if (application.status === "completed") {
        const workerId = application.workerId;
        const companyUserId = application.companyUserId;
        const jobId = application.jobId;

        // Worker rates company
        const workerRating = 3 + Math.floor(Math.random() * 3); // 3-5 stars
        const workerIsAnonymous = Math.random() < 0.3;

        await queryRunner.query(`
          INSERT INTO ratings (
            id, "ratedById", "ratedUserId", "jobId", stars, comment, "isAnonymous"
          ) VALUES (
            uuid_generate_v4(), '${workerId}', '${companyUserId}', '${jobId}',
            ${workerRating}, 'Good job opportunity, would work with this company again.', ${workerIsAnonymous}
          );
        `);

        // Company rates worker
        const companyRating = 3 + Math.floor(Math.random() * 3); // 3-5 stars
        await queryRunner.query(`
          INSERT INTO ratings (
            id, "ratedById", "ratedUserId", "jobId", stars, comment, "isAnonymous"
          ) VALUES (
            uuid_generate_v4(), '${companyUserId}', '${workerId}', '${jobId}',
            ${companyRating}, 'Good worker, completed the job as expected.', false
          );
        `);
      }
    }
  }

  // ==================== PAYOUTS ====================
  console.log("Creating payouts...");

  // Create payouts for completed applications
  for (let i = 0; i < applicationIds.length; i++) {
    // Get application details
    const applicationResult = await queryRunner.query(`
      SELECT a.id, a.status, a."workerId", j.id as "jobId", j."hourlyRate", j."estimatedHours"
      FROM applications a
      JOIN jobs j ON a."jobId" = j.id
      WHERE a.id = '${applicationIds[i]}'
    `);

    if (applicationResult.length === 0) continue;

    const application = applicationResult[0];

    // Only create payouts for completed applications
    if (application.status === "completed") {
      const workerId = application.workerId;
      const jobId = application.jobId;
      const hourlyRate = parseFloat(application.hourlyRate);
      const estimatedHours = parseInt(application.estimatedHours);

      // Calculate payout amounts
      const grossAmount = hourlyRate * estimatedHours;
      const platformFee = grossAmount * 0.1; // 10% platform fee
      const netAmount = grossAmount - platformFee;

      // Determine payout status
      let payoutStatus = "pending";
      if (Math.random() < 0.7) {
        payoutStatus = "completed";
      } else if (Math.random() < 0.5) {
        payoutStatus = "processing";
      }

      // Create payout record
      const transactionId =
        payoutStatus === "completed" ? `'TX${1000000 + i}'` : "NULL";
      const paymentDate = payoutStatus === "completed" ? "NOW()" : "NULL";

      await queryRunner.query(`
        INSERT INTO payouts (
          id, "workerId", "jobId", "grossAmount", "commission", "netAmount", "platformFee",
          "taxAmount", status, "paymentMethod", "transactionId", "paymentDate"
        ) VALUES (
          uuid_generate_v4(), '${workerId}', '${jobId}', ${grossAmount}, ${platformFee}, ${netAmount}, ${platformFee},
          0, '${payoutStatus}', 'bank_transfer', ${transactionId}, ${paymentDate}
        );
      `);
    }
  }

  // ==================== DISPUTES ====================
  console.log("Creating disputes...");

  // Create disputes for some applications (about 10%)
  for (let i = 0; i < applicationIds.length; i++) {
    if (Math.random() < 0.1) {
      // Get application details
      const applicationResult = await queryRunner.query(`
        SELECT a.id, a.status, a."workerId", j."companyId", j.id as "jobId", c."userId" as "companyUserId"
        FROM applications a
        JOIN jobs j ON a."jobId" = j.id
        JOIN companies c ON j."companyId" = c.id
        WHERE a.id = '${applicationIds[i]}'
      `);

      if (applicationResult.length === 0) continue;

      const application = applicationResult[0];
      const workerId = application.workerId;
      const companyUserId = application.companyUserId;
      const jobId = application.jobId;

      // Determine who raised the dispute
      const workerRaisedDispute = Math.random() < 0.5;
      const raisedById = workerRaisedDispute ? workerId : companyUserId;
      const againstId = workerRaisedDispute ? companyUserId : workerId;

      // Determine dispute status
      let disputeStatus = "open";
      if (Math.random() < 0.3) {
        disputeStatus = "under-review";
      } else if (Math.random() < 0.2) {
        disputeStatus = "resolved";
      }

      // Create dispute
      const disputeReasons = [
        "Payment dispute",
        "Job conditions were different than described",
        "Worker didn''t complete the job as required",
        "Communication issues",
        "Safety concerns at the job site",
      ];

      const reasonIndex = Math.floor(Math.random() * disputeReasons.length);

      const reason = disputeReasons[reasonIndex];
      const resolvedById =
        disputeStatus === "resolved" ? `'${adminIds[0]}'` : "NULL";
      const resolvedAt = disputeStatus === "resolved" ? "NOW()" : "NULL";
      const resolution =
        disputeStatus === "resolved"
          ? "'Issue resolved through mediation.'"
          : "NULL";

      await queryRunner.query(`
        INSERT INTO disputes (
          id, "raisedById", "againstId", "jobId", reason, description, status,
          "resolvedById", "resolvedAt", resolution
        ) VALUES (
          uuid_generate_v4(), '${raisedById}', '${againstId}', '${jobId}',
          '${reason}', 'Detailed description of the dispute issue.',
          '${disputeStatus}',
          ${resolvedById},
          ${resolvedAt},
          ${resolution}
        );
      `);
    }
  }

  // ==================== FAVORITES ====================
  console.log("Creating favorites...");

  // Create favorites for some workers (each worker saves 2-5 jobs)
  for (let i = 0; i < workerIds.length; i++) {
    const workerId = workerIds[i];

    // Determine how many jobs to favorite
    const numFavorites = 2 + Math.floor(Math.random() * 4);

    // Create a set to track which jobs this worker has already favorited
    const favoritedJobs = new Set();

    for (let j = 0; j < numFavorites; j++) {
      // Find a job this worker hasn't saved yet
      let jobIndex: number;
      do {
        jobIndex = Math.floor(Math.random() * jobIds.length);
      } while (favoritedJobs.has(jobIndex));

      favoritedJobs.add(jobIndex);
      const jobId = jobIds[jobIndex];

      // Create favorite
      const randomDays = Math.floor(Math.random() * 10);

      await queryRunner.query(`
        INSERT INTO favorites (
          id, "workerId", "jobId", "createdAt"
        ) VALUES (
          uuid_generate_v4(), '${workerId}', '${jobId}', NOW() - INTERVAL '${randomDays} days'
        );
      `);
    }
  }

  // ==================== CHATS ====================
  console.log("Creating chats...");

  // Create chats for accepted applications
  for (let i = 0; i < applicationIds.length; i++) {
    // Get application details
    const applicationResult = await queryRunner.query(`
      SELECT a.id, a.status, a."workerId", j."companyId", j.id as "jobId", c."userId" as "companyUserId"
      FROM applications a
      JOIN jobs j ON a."jobId" = j.id
      JOIN companies c ON j."companyId" = c.id
      WHERE a.id = '${applicationIds[i]}'
    `);

    if (applicationResult.length === 0) continue;

    const application = applicationResult[0];

    // Only create chats for accepted or completed applications
    if (
      application.status === "accepted" ||
      application.status === "completed"
    ) {
      const workerId = application.workerId;
      const companyId = application.companyId;
      const companyUserId = application.companyUserId;
      const jobId = application.jobId;

      // Create chat
      const unreadWorkerCount = Math.floor(Math.random() * 5);
      const unreadCompanyCount = Math.floor(Math.random() * 5);

      const chatResult = await queryRunner.query(`
        INSERT INTO chats (
          id, "workerId", "companyId", "jobId", "unreadWorkerCount", "unreadCompanyCount"
        ) VALUES (
          uuid_generate_v4(), '${workerId}', '${companyId}', '${jobId}', ${unreadWorkerCount}, ${unreadCompanyCount}
        ) RETURNING id;
      `);

      const chatId = chatResult[0].id;

      // Create chat messages (3-10 messages per chat)
      const numMessages = 3 + Math.floor(Math.random() * 8);

      const messageTemplates = [
        "Hi there! I''m interested in discussing the job details.",
        "When would you like me to start?",
        "What specific skills are you looking for?",
        "I''m available to start immediately.",
        "Do you have any specific requirements for this job?",
        "I have a question about the job location.",
        "Can you provide more details about the job?",
        "I''m looking forward to working with you!",
        "What time should I arrive on the first day?",
        "Do I need to bring any specific tools or equipment?",
        "Thanks for the opportunity!",
        "I have previous experience with similar jobs.",
        "Is there parking available at the job site?",
        "How many other workers will be on site?",
        "What''s the dress code for this job?",
      ];

      for (let j = 0; j < numMessages; j++) {
        // Determine sender
        const isWorkerMessage = j % 2 === 0;
        const senderId = isWorkerMessage ? workerId : companyUserId;
        const senderType = isWorkerMessage ? "worker" : "company";

        // Select message
        const messageIndex = Math.floor(
          Math.random() * messageTemplates.length
        );
        const message = messageTemplates[messageIndex];

        // Create message
        const isRead = Math.random() < 0.7;
        const randomDays = Math.floor(Math.random() * 5);
        const randomHours = Math.floor(Math.random() * 24);

        await queryRunner.query(`
          INSERT INTO chat_messages (
            id, "chatId", "senderId", "senderType", message, "isRead", "createdAt"
          ) VALUES (
            uuid_generate_v4(), '${chatId}', '${senderId}', '${senderType}', '${message}',
            ${isRead}, NOW() - INTERVAL '${randomDays} days ${randomHours} hours'
          );
        `);
      }
    }
  }

  // ==================== NOTIFICATIONS ====================
  console.log("Creating notifications...");

  // Create notifications for all users
  const allUserIds = [...adminIds, ...companyUserIds, ...workerIds];

  for (let i = 0; i < allUserIds.length; i++) {
    const userId = allUserIds[i];

    // Create 3-8 notifications per user
    const numNotifications = 3 + Math.floor(Math.random() * 6);

    const notificationTitles = [
      "New job application",
      "Application status updated",
      "New message received",
      "Payment processed",
      "Document verification complete",
      "Job completed",
      "New job posted in your area",
      "Profile verification required",
      "Rating received",
      "Dispute update",
    ];

    const notificationTypes = [
      "application",
      "job",
      "chat",
      "payout",
      "document",
      "job",
      "job",
      "verification",
      "rating",
      "dispute",
    ];

    for (let j = 0; j < numNotifications; j++) {
      const titleIndex = Math.floor(Math.random() * notificationTitles.length);
      const title = notificationTitles[titleIndex];
      const type = notificationTypes[titleIndex];
      const isRead = Math.random() < 0.6;

      const message = `This is a notification about ${type}. Please check your account for details.`;
      const isActionRequired = Math.random() < 0.3;

      await queryRunner.query(`
        INSERT INTO notifications (
          id, "userId", title, message, type, "isRead", "readAt", "isActionRequired"
        ) VALUES (
          uuid_generate_v4(), '${userId}', '${title}',
          '${message}', '${type}', ${isRead}, ${
        isRead ? "NOW()" : "NULL"
      }, ${isActionRequired}
        );
      `);
    }
  }

  // ==================== BADGES ====================
  console.log("Creating badges...");

  // Create badge definitions
  const badgeNames = [
    "First Job Completed",
    "5 Jobs Completed",
    "10 Jobs Completed",
    "Perfect Attendance",
    "Top Rated Worker",
    "Quick Responder",
    "Verification Champion",
    "Reliable Worker",
    "Job Explorer",
    "Veteran Worker",
  ];

  const badgeDescriptions = [
    "Completed your first job successfully.",
    "Completed 5 jobs successfully.",
    "Completed 10 jobs successfully.",
    "Never missed a scheduled job.",
    "Maintained a 5-star rating across multiple jobs.",
    "Responds to job inquiries within 1 hour.",
    "Completed all verification steps.",
    "Completed 10 consecutive jobs without issues.",
    "Applied to jobs in 5 different categories.",
    "Been on the platform for over a year.",
  ];

  const badgeCategories = [
    "jobs",
    "jobs",
    "jobs",
    "jobs",
    "trust",
    "activity",
    "trust",
    "jobs",
    "activity",
    "special",
  ];

  const badgeRarities = [
    "common",
    "common",
    "uncommon",
    "rare",
    "epic",
    "uncommon",
    "rare",
    "uncommon",
    "rare",
    "legendary",
  ];

  const badgeIds: string[] = [];

  for (let i = 0; i < badgeNames.length; i++) {
    const iconName = `badge_icon_${i + 1}.png`;
    const requiredValue = (i + 1) * 5;

    const result = await queryRunner.query(`
      INSERT INTO badges (
        id, name, description, icon, category, rarity, "requiredValue", "isActive"
      ) VALUES (
        uuid_generate_v4(), '${badgeNames[i]}', '${badgeDescriptions[i]}',
        '${iconName}', '${badgeCategories[i]}', '${badgeRarities[i]}',
        ${requiredValue}, true
      ) RETURNING id;
    `);

    badgeIds.push(result[0].id);
  }

  // Assign badges to some workers
  for (let i = 0; i < workerIds.length; i++) {
    const workerId = workerIds[i];

    // Determine how many badges this worker has earned (0-5)
    const numBadges = Math.floor(Math.random() * 6);

    // Create a set to track which badges this worker has already earned
    const earnedBadges = new Set();

    for (let j = 0; j < numBadges; j++) {
      // Find a badge this worker hasn't earned yet
      let badgeIndex: number;
      do {
        badgeIndex = Math.floor(Math.random() * badgeIds.length);
      } while (earnedBadges.has(badgeIndex));

      earnedBadges.add(badgeIndex);
      const badgeId = badgeIds[badgeIndex];

      // Create user badge
      const randomDays = Math.floor(Math.random() * 30);

      await queryRunner.query(`
        INSERT INTO user_badges (
          id, "userId", "badgeId", "awardedAt"
        ) VALUES (
          uuid_generate_v4(), '${workerId}', '${badgeId}', NOW() - INTERVAL '${randomDays} days'
        );
      `);
    }
  }

  // ==================== GENERIC CHAT SYSTEM ====================
  console.log("Creating generic chat conversations...");

  // Create chat conversations between companies and workers
  const chatIds: string[] = [];

  for (let i = 0; i < 15; i++) {
    // Create 15 chat conversations
    const companyUserId = companyUserIds[i % companyUserIds.length];
    const workerUserId = workerIds[i % workerIds.length];
    const jobId = jobIds[i % jobIds.length];

    // Create generic chat
    const chatResult = await queryRunner.query(`
      INSERT INTO generic_chats (
        id, type, "relatedEntityId", "relatedEntityType", title, description, "isActive"
      ) VALUES (
        uuid_generate_v4(), 'job_discussion', '${jobId}', 'job',
        'Job Discussion: ${
          jobData[i % jobData.length]?.title || "Job Discussion"
        }',
        'Chat conversation regarding job application and requirements', true
      ) RETURNING id;
    `);

    const chatId = chatResult[0].id;
    chatIds.push(chatId);

    // Add participants
    await queryRunner.query(`
      INSERT INTO chat_participants (
        id, "chatId", "userId", role, "joinedAt", "isActive"
      ) VALUES (
        uuid_generate_v4(), '${chatId}', '${companyUserId}', 'admin',
        NOW() - INTERVAL '${Math.floor(Math.random() * 14)} days', true
      );
    `);

    await queryRunner.query(`
      INSERT INTO chat_participants (
        id, "chatId", "userId", role, "joinedAt", "isActive"
      ) VALUES (
        uuid_generate_v4(), '${chatId}', '${workerUserId}', 'member',
        NOW() - INTERVAL '${Math.floor(Math.random() * 14)} days', true
      );
    `);

    // Create chat messages
    const messageCount = 5 + Math.floor(Math.random() * 10); // 5-15 messages per chat
    const messages = [
      "Hi, I'm interested in this job opportunity. Could you provide more details?",
      "Hello! Thanks for your interest. The job involves data entry work for our client database.",
      "That sounds great. What are the working hours and requirements?",
      "The work is flexible, but we prefer someone who can work 4-6 hours daily. Do you have experience with Excel?",
      "Yes, I have 3 years of experience with Excel and data management. When can I start?",
      "Perfect! We can start next Monday. I'll send you the training materials.",
      "Thank you! I'm looking forward to working with your team.",
      "Great! Please confirm your availability for the orientation session.",
      "I'm available anytime next week. What time works best for you?",
      "How about Tuesday at 10 AM? We can do a video call to go over everything.",
      "Tuesday at 10 AM works perfectly for me. Should I prepare anything?",
      "Just bring your questions and we'll cover everything else. See you then!",
      "Sounds good! Thank you for the opportunity.",
      "You're welcome! Looking forward to working together.",
      "Is there anything else I should know before we start?",
    ];

    for (let j = 0; j < messageCount; j++) {
      const isFromCompany = j % 2 === 0;
      const senderId = isFromCompany ? companyUserId : workerUserId;
      const messageText = messages[j % messages.length];
      const hoursAgo = (messageCount - j) * 2; // Spread messages over time

      await queryRunner.query(`
        INSERT INTO chat_messages (
          id, "chatId", "senderId", content, type, status,
          "sentAt", "deliveredAt", "readAt"
        ) VALUES (
          uuid_generate_v4(), '${chatId}', '${senderId}', '${messageText}', 'text', 'read',
          NOW() - INTERVAL '${hoursAgo} hours',
          NOW() - INTERVAL '${hoursAgo - 1} hours',
          NOW() - INTERVAL '${Math.max(0, hoursAgo - 2)} hours'
        );
      `);
    }
  }

  // ==================== NOTIFICATIONS ====================
  console.log("Creating notifications...");

  const notificationTypes = [
    {
      type: "job_application",
      title: "New Job Application",
      message: "You have received a new job application",
    },
    {
      type: "application_accepted",
      title: "Application Accepted",
      message: "Your job application has been accepted",
    },
    {
      type: "application_rejected",
      title: "Application Rejected",
      message: "Your job application has been rejected",
    },
    {
      type: "job_completed",
      title: "Job Completed",
      message: "Job has been marked as completed",
    },
    {
      type: "payment_received",
      title: "Payment Received",
      message: "You have received a payment",
    },
    {
      type: "new_message",
      title: "New Message",
      message: "You have received a new message",
    },
    {
      type: "dispute_raised",
      title: "Dispute Raised",
      message: "A dispute has been raised for your job",
    },
    {
      type: "document_verified",
      title: "Document Verified",
      message: "Your document has been verified",
    },
    {
      type: "trust_score_updated",
      title: "Trust Score Updated",
      message: "Your trust score has been updated",
    },
    {
      type: "job_reminder",
      title: "Job Reminder",
      message: "Don't forget about your upcoming job",
    },
  ];

  // Create notifications for all users
  const allUserIdsForNotifications = [
    ...adminIds,
    ...companyUserIds,
    ...workerIds,
  ];

  for (let i = 0; i < allUserIdsForNotifications.length; i++) {
    const userId = allUserIdsForNotifications[i];
    const numNotifications = 3 + Math.floor(Math.random() * 5); // 3-7 notifications per user

    for (let j = 0; j < numNotifications; j++) {
      const notification =
        notificationTypes[Math.floor(Math.random() * notificationTypes.length)];
      const isRead = Math.random() < 0.6; // 60% read
      const daysAgo = Math.floor(Math.random() * 30);

      await queryRunner.query(`
        INSERT INTO notifications (
          id, "userId", type, title, message, "isRead",
          "createdAt", ${isRead ? '"readAt",' : ""}
          metadata
        ) VALUES (
          uuid_generate_v4(), '${userId}', '${notification.type}', '${
        notification.title
      }',
          '${notification.message}', ${isRead},
          NOW() - INTERVAL '${daysAgo} days',
          ${
            isRead ? `NOW() - INTERVAL '${Math.max(0, daysAgo - 1)} days',` : ""
          }
          '{}'
        );
      `);
    }
  }

  // ==================== PASSWORD RESET TOKENS ====================
  console.log("Creating password reset tokens...");

  // Create some recent password reset tokens for testing
  for (let i = 0; i < 5; i++) {
    const userId =
      allUserIdsForNotifications[
        Math.floor(Math.random() * allUserIdsForNotifications.length)
      ];
    const token = `reset_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2)}`;
    const isUsed = Math.random() < 0.4; // 40% used
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + (isUsed ? -2 : 24)); // Expired if used, valid if not

    await queryRunner.query(`
      INSERT INTO password_reset_tokens (
        id, "userId", token, "expiresAt", used
      ) VALUES (
        uuid_generate_v4(), '${userId}', '${token}',
        '${expiresAt.toISOString()}', ${isUsed}
      );
    `);
  }

  // ==================== SETTINGS ====================
  console.log("Creating system settings...");

  const systemSettings = [
    {
      key: "platform_name",
      value: "JobMatch Platform",
      description: "Platform display name",
      type: "string",
    },
    {
      key: "platform_commission",
      value: "10",
      description: "Platform commission percentage",
      type: "number",
    },
    {
      key: "min_trust_score",
      value: "50",
      description: "Minimum trust score for job applications",
      type: "number",
    },
    {
      key: "max_applications_per_day",
      value: "20",
      description: "Maximum applications per worker per day",
      type: "number",
    },
    {
      key: "email_notifications_enabled",
      value: "true",
      description: "Enable email notifications",
      type: "boolean",
    },
    {
      key: "sms_notifications_enabled",
      value: "true",
      description: "Enable SMS notifications",
      type: "boolean",
    },
    {
      key: "auto_approve_verified_workers",
      value: "false",
      description: "Auto-approve applications from verified workers",
      type: "boolean",
    },
    {
      key: "payment_processing_fee",
      value: "2.5",
      description: "Payment processing fee percentage",
      type: "number",
    },
    {
      key: "dispute_resolution_time",
      value: "7",
      description: "Days to resolve disputes",
      type: "number",
    },
    {
      key: "worker_rating_required",
      value: "true",
      description: "Require worker ratings after job completion",
      type: "boolean",
    },
  ];

  for (const setting of systemSettings) {
    await queryRunner.query(`
      INSERT INTO settings (
        id, key, value, description, type, "isSystem"
      ) VALUES (
        uuid_generate_v4(), '${setting.key}', '${setting.value}',
        '${setting.description}', '${setting.type}', true
      );
    `);
  }

  // ==================== OTP RECORDS ====================
  console.log("Creating OTP records...");

  // Create some recent OTP records for testing
  for (let i = 0; i < 10; i++) {
    const isEmail = Math.random() < 0.5;
    const identifier = isEmail ? `test${i}@example.com` : `+1${5000000 + i}`;
    const code = String(Math.floor(100000 + Math.random() * 900000));
    const verified = Math.random() < 0.7; // 70% verified
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + (verified ? -5 : 10)); // Expired if verified, valid if not

    await queryRunner.query(`
      INSERT INTO otps (
        id, ${isEmail ? "email" : "phone"}, code, "expiresAt", verified
      ) VALUES (
        uuid_generate_v4(), '${identifier}', '${code}',
        '${expiresAt.toISOString()}', ${verified}
      );
    `);
  }

  // ==================== JOB TEMPLATES ====================
  console.log("Creating job templates...");

  const jobTemplates = [
    {
      title: "Data Entry Project",
      description:
        "Standard data entry work requiring attention to detail and fast typing speed.",
      duration: 8,
      trustScoreRequired: 60,
      paymentAmount: 150,
      requiresLaptop: true,
      requiresSmartphone: false,
      isEmergencyJob: false,
    },
    {
      title: "Delivery Service",
      description:
        "Local delivery service requiring valid driver's license and reliable vehicle.",
      duration: 4,
      trustScoreRequired: 70,
      paymentAmount: 80,
      requiresLaptop: false,
      requiresSmartphone: true,
      isEmergencyJob: true,
    },
    {
      title: "Event Staff",
      description:
        "Professional event staffing for corporate events and conferences.",
      duration: 6,
      trustScoreRequired: 75,
      paymentAmount: 180,
      requiresLaptop: false,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
    {
      title: "Customer Support",
      description:
        "Remote customer support role requiring excellent communication skills.",
      duration: 8,
      trustScoreRequired: 65,
      paymentAmount: 160,
      requiresLaptop: true,
      requiresSmartphone: true,
      isEmergencyJob: false,
    },
    {
      title: "Content Moderation",
      description: "Content moderation and review for social media platforms.",
      duration: 6,
      trustScoreRequired: 80,
      paymentAmount: 120,
      requiresLaptop: true,
      requiresSmartphone: false,
      isEmergencyJob: false,
    },
  ];

  for (let i = 0; i < jobTemplates.length; i++) {
    const template = jobTemplates[i];
    const companyUserId = companyUserIds[i % companyUserIds.length];

    await queryRunner.query(`
      INSERT INTO job_templates (
        id, title, description, duration, "trustScoreRequired", "paymentAmount",
        "requiresLaptop", "requiresSmartphone", "isEmergencyJob", "postedBy"
      ) VALUES (
        uuid_generate_v4(), '${template.title}', '${template.description}',
        ${template.duration}, ${template.trustScoreRequired}, ${template.paymentAmount},
        ${template.requiresLaptop}, ${template.requiresSmartphone}, ${template.isEmergencyJob},
        '${companyUserId}'
      );
    `);
  }

  // ==================== ESCROW ACCOUNTS ====================
  console.log("Creating escrow accounts...");

  // Create escrow accounts for jobs that are in progress or completed
  for (let i = 0; i < jobIds.length; i++) {
    const jobId = jobIds[i];
    const companyUserId = companyUserIds[i % companyUserIds.length];

    // Only create escrow for jobs that would need it (in progress or completed)
    if (i < 5) {
      // First 5 jobs get escrow accounts
      const amount = 500 + Math.floor(Math.random() * 2000); // $500-2500
      const status = i < 2 ? "held" : i < 4 ? "released" : "pending";
      const workerId =
        status === "released" ? workerIds[i % workerIds.length] : null;

      await queryRunner.query(`
        INSERT INTO escrow_accounts (
          id, "jobId", "companyId", amount, currency, status,
          ${workerId ? '"workerId",' : ""} "transactionId", description
        ) VALUES (
          uuid_generate_v4(), '${jobId}', '${companyUserId}', ${amount}, 'INR', '${status}',
          ${workerId ? `'${workerId}',` : ""} 'TXN${
        1000000 + i
      }', 'Escrow for job completion'
        );
      `);
    }
  }

  // ==================== ACTIVITY LOGS ====================
  console.log("Creating activity logs...");

  const activityTypes = [
    {
      action: "user_login",
      entityType: "user",
      description: "User logged into the platform",
    },
    {
      action: "job_created",
      entityType: "job",
      description: "New job was created",
    },
    {
      action: "application_submitted",
      entityType: "application",
      description: "Job application was submitted",
    },
    {
      action: "application_accepted",
      entityType: "application",
      description: "Job application was accepted",
    },
    {
      action: "job_completed",
      entityType: "job",
      description: "Job was marked as completed",
    },
    {
      action: "payment_processed",
      entityType: "payout",
      description: "Payment was processed",
    },
    {
      action: "dispute_raised",
      entityType: "dispute",
      description: "Dispute was raised",
    },
    {
      action: "document_uploaded",
      entityType: "document",
      description: "Document was uploaded for verification",
    },
    {
      action: "profile_updated",
      entityType: "user",
      description: "User profile was updated",
    },
    {
      action: "rating_submitted",
      entityType: "rating",
      description: "Rating was submitted",
    },
  ];

  // Create activity logs for all users
  const allUserIdsForLogs = [...adminIds, ...companyUserIds, ...workerIds];

  for (let i = 0; i < 100; i++) {
    // Create 100 activity log entries
    const userId =
      allUserIdsForLogs[Math.floor(Math.random() * allUserIdsForLogs.length)];
    const activity =
      activityTypes[Math.floor(Math.random() * activityTypes.length)];

    await queryRunner.query(`
      INSERT INTO activity_logs (
        id, "userId", action, "entityType", description,
        metadata, "ipAddress", "userAgent"
      ) VALUES (
        uuid_generate_v4(), '${userId}', '${activity.action}', '${
      activity.entityType
    }',
        '${activity.description}', '{}', '192.168.1.${Math.floor(
      Math.random() * 255
    )}',
        'Mozilla/5.0 (compatible; JobPlatform/1.0)'
      );
    `);
  }

  console.log("Seed data creation complete!");
};

/**
 * Migration to remove test data
 */
export const down = async (queryRunner: QueryRunner): Promise<void> => {
  console.log("Removing all test data...");

  // Delete all test data in the correct order to avoid foreign key constraints
  await queryRunner.query(`DELETE FROM activity_logs;`);
  await queryRunner.query(`DELETE FROM password_reset_tokens;`);
  await queryRunner.query(`DELETE FROM user_badges;`);
  await queryRunner.query(`DELETE FROM badges;`);
  await queryRunner.query(`DELETE FROM notifications;`);
  await queryRunner.query(`DELETE FROM chat_messages;`);
  await queryRunner.query(`DELETE FROM chat_participants;`);
  await queryRunner.query(`DELETE FROM generic_chats;`);
  await queryRunner.query(`DELETE FROM chats;`); // Legacy chat system
  await queryRunner.query(`DELETE FROM escrow_accounts;`);
  await queryRunner.query(`DELETE FROM job_templates;`);
  await queryRunner.query(`DELETE FROM otps;`);
  await queryRunner.query(`DELETE FROM settings WHERE "isSystem" = true;`);
  await queryRunner.query(`DELETE FROM favorites;`);
  await queryRunner.query(`DELETE FROM reports;`);
  await queryRunner.query(`DELETE FROM ratings;`);
  await queryRunner.query(`DELETE FROM disputes;`);
  await queryRunner.query(`DELETE FROM payouts;`);
  await queryRunner.query(`DELETE FROM applications;`);
  await queryRunner.query(`DELETE FROM jobs;`);
  await queryRunner.query(`DELETE FROM trust_score_logs;`);
  await queryRunner.query(`DELETE FROM documents;`);
  await queryRunner.query(`DELETE FROM companies;`);

  // Delete all test users
  await queryRunner.query(`
    DELETE FROM users
    WHERE email LIKE '%@jobplatform.com'
    OR email LIKE '%@email.com'
    OR email LIKE '<EMAIL>'
    OR email LIKE '<EMAIL>'
    OR email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');
  `);

  console.log("All test data removed successfully.");
};
