import { QueryRunner, MigrationInterface } from "typeorm";

/**
 * Migration to create the initial database schema
 * This migration creates all tables and enums to match the current entity structure
 */
export class CreateInitialSchema1704067200000 implements MigrationInterface {
  name = "CreateInitialSchema1704067200000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Enable UUID extension
    await queryRunner.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    `);

    // Create enums first - matching exact entity enum values
    await queryRunner.query(`
      CREATE TYPE "user_role_enum" AS ENUM ('admin', 'company', 'worker', 'super_admin');
    `);

    await queryRunner.query(`
      CREATE TYPE "job_status_enum" AS ENUM ('draft', 'open', 'in_progress', 'completed', 'cancelled');
    `);

    // Updated ApplicationStatus enum to match shared types
    await queryRunner.query(`
      CREATE TYPE "application_status_enum" AS ENUM ('pending', 'shortlisted', 'accepted', 'rejected', 'hired', 'withdrawn', 'cancelled', 'completed', 'no_show');
    `);

    await queryRunner.query(`
      CREATE TYPE "payout_status_enum" AS ENUM ('pending', 'processing', 'completed', 'failed');
    `);

    // Fixed dispute status enum - using underscore instead of hyphen
    await queryRunner.query(`
      CREATE TYPE "dispute_status_enum" AS ENUM ('open', 'under_review', 'resolved', 'rejected', 'closed');
    `);

    await queryRunner.query(`
      CREATE TYPE "document_type_enum" AS ENUM ('id_proof', 'address_proof', 'profile_photo', 'education_certificate', 'work_certificate', 'company_registration', 'tax_document', 'registration_certificate', 'tax_certificate', 'business_license', 'id_card', 'other');
    `);

    await queryRunner.query(`
      CREATE TYPE "verification_status_enum" AS ENUM ('pending', 'verified', 'rejected', 'approved');
    `);

    await queryRunner.query(`
      CREATE TYPE "badge_category_enum" AS ENUM ('jobs', 'trust', 'activity', 'special');
    `);

    await queryRunner.query(`
      CREATE TYPE "badge_rarity_enum" AS ENUM ('common', 'uncommon', 'rare', 'epic', 'legendary');
    `);

    await queryRunner.query(`
      CREATE TYPE "chat_type_enum" AS ENUM ('worker_company', 'support', 'group', 'admin_user', 'general');
    `);

    await queryRunner.query(`
      CREATE TYPE "participant_role_enum" AS ENUM ('worker', 'company', 'admin', 'support', 'moderator', 'member');
    `);

    await queryRunner.query(`
      CREATE TYPE "message_type_enum" AS ENUM ('text', 'image', 'file', 'system', 'notification');
    `);

    await queryRunner.query(`
      CREATE TYPE "message_status_enum" AS ENUM ('sent', 'delivered', 'read', 'failed');
    `);

    await queryRunner.query(`
      CREATE TYPE "escrow_status_enum" AS ENUM ('pending', 'held', 'released', 'refunded', 'disputed');
    `);

    // Add report status enum for reports table
    await queryRunner.query(`
      CREATE TYPE "report_status_enum" AS ENUM ('pending', 'in_progress', 'completed', 'failed');
    `);

    // Create users table - matching User entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        username VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        role "user_role_enum" NOT NULL DEFAULT 'worker',
        "trustScore" INTEGER DEFAULT 70 CHECK ("trustScore" >= 0 AND "trustScore" <= 100),
        "isKycVerified" BOOLEAN DEFAULT FALSE,
        "kycVerificationDate" TIMESTAMP WITH TIME ZONE,
        "isActive" BOOLEAN DEFAULT TRUE,
        "isBanned" BOOLEAN DEFAULT FALSE,
        "banReason" TEXT,
        bio TEXT,
        "profilePicture" TEXT,
        "upiId" VARCHAR(255),
        "deviceTokens" TEXT[],
        "emailVerified" BOOLEAN DEFAULT FALSE,
        "phoneVerified" BOOLEAN DEFAULT FALSE,
        metadata JSONB,
        "lastLoginAt" TIMESTAMP WITH TIME ZONE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for users table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_users_username" ON users (username);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_users_email" ON users (email);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_users_phone" ON users (phone);`
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX IF NOT EXISTS "UQ_users_email" ON users (email) WHERE email IS NOT NULL;`
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX IF NOT EXISTS "UQ_users_phone" ON users (phone) WHERE phone IS NOT NULL;`
    );

    // Create companies table - matching Company entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS companies (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "userId" UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "registrationNumber" VARCHAR(255),
        "taxId" VARCHAR(255),
        website VARCHAR(255),
        size VARCHAR(50),
        industry VARCHAR(100),
        "foundedYear" INTEGER,
        "headquartersAddress" TEXT,
        "headquartersCity" VARCHAR(100),
        "headquartersState" VARCHAR(100),
        "headquartersPostalCode" VARCHAR(20),
        "headquartersCountry" VARCHAR(100),
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create jobs table - matching Job entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jobs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "companyId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        location VARCHAR(255) NOT NULL,
        city VARCHAR(100),
        state VARCHAR(100),
        country VARCHAR(100),
        "postalCode" VARCHAR(20),
        latitude DECIMAL(10, 8),
        longitude DECIMAL(11, 8),
        "startDateTime" TIMESTAMP NOT NULL,
        "endDateTime" TIMESTAMP NOT NULL,
        "payRate" DECIMAL(10, 2) NOT NULL,
        "payRateType" VARCHAR(20) DEFAULT 'hourly',
        "estimatedHours" INTEGER,
        "trustScoreRequired" INTEGER DEFAULT 0 CHECK ("trustScoreRequired" >= 0 AND "trustScoreRequired" <= 100),
        "isEmergencyJob" BOOLEAN DEFAULT FALSE,
        "requiresLaptop" BOOLEAN DEFAULT FALSE,
        "requiresSmartphone" BOOLEAN DEFAULT FALSE,
        status "job_status_enum" DEFAULT 'open',
        "maxPositions" INTEGER DEFAULT 1,
        "filledPositions" INTEGER DEFAULT 0,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for jobs table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_jobs_companyId" ON jobs ("companyId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_jobs_status" ON jobs (status);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_jobs_location" ON jobs (city, state);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_jobs_startDateTime" ON jobs ("startDateTime");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_jobs_endDateTime" ON jobs ("endDateTime");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_jobs_payRate" ON jobs ("payRate");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_jobs_latitude_longitude" ON jobs (latitude, longitude);`
    );

    // Create applications table - matching Application entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS applications (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
        "workerId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        status "application_status_enum" DEFAULT 'pending',
        "coverLetter" TEXT,
        "isEmergencyJob" BOOLEAN DEFAULT FALSE,
        "rejectionReason" TEXT,
        "cancellationReason" TEXT,
        "cancelledAt" TIMESTAMP WITH TIME ZONE,
        "completedAt" TIMESTAMP WITH TIME ZONE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE("jobId", "workerId")
      );
    `);

    // Create indexes for applications table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_applications_jobId" ON applications ("jobId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_applications_workerId" ON applications ("workerId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_applications_status" ON applications (status);`
    );

    // Create documents table - matching Document entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS documents (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "userId" UUID REFERENCES users(id) ON DELETE CASCADE,
        "documentType" "document_type_enum" NOT NULL,
        "documentUrl" TEXT NOT NULL,
        "documentNumber" VARCHAR(100),
        "verificationStatus" "verification_status_enum" DEFAULT 'pending',
        "verifiedBy" UUID REFERENCES users(id),
        "verifiedAt" TIMESTAMP WITH TIME ZONE,
        "rejectionReason" TEXT,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for documents table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_documents_userId" ON documents ("userId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_documents_verificationStatus" ON documents ("verificationStatus");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_documents_documentType" ON documents ("documentType");`
    );

    // Create ratings table - matching Rating entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS ratings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "ratedById" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "ratedUserId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
        stars INTEGER NOT NULL CHECK (stars >= 1 AND stars <= 5),
        comment TEXT,
        "isAnonymous" BOOLEAN DEFAULT FALSE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE("ratedById", "ratedUserId", "jobId")
      );
    `);

    // Create indexes for ratings table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_ratings_ratedUserId" ON ratings ("ratedUserId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_ratings_jobId" ON ratings ("jobId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_ratings_ratedById" ON ratings ("ratedById");`
    );

    // Create payouts table - matching Payout entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS payouts (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "workerId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
        "grossAmount" DECIMAL(10, 2) NOT NULL,
        commission DECIMAL(10, 2) NOT NULL,
        "netAmount" DECIMAL(10, 2) NOT NULL,
        "platformFee" DECIMAL(10, 2) NOT NULL,
        "taxAmount" DECIMAL(10, 2) DEFAULT 0,
        status "payout_status_enum" DEFAULT 'pending',
        "paymentMethod" VARCHAR(50),
        "transactionId" VARCHAR(255),
        "paymentDate" TIMESTAMP WITH TIME ZONE,
        notes TEXT,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for payouts table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_payouts_workerId" ON payouts ("workerId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_payouts_jobId" ON payouts ("jobId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_payouts_status" ON payouts (status);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_payouts_paymentDate" ON payouts ("paymentDate");`
    );

    // Create disputes table - matching Dispute entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS disputes (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "raisedById" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "againstId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
        reason TEXT NOT NULL,
        description TEXT,
        status "dispute_status_enum" DEFAULT 'open',
        "resolvedById" UUID REFERENCES users(id),
        "resolvedAt" TIMESTAMP WITH TIME ZONE,
        resolution TEXT,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for disputes table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_disputes_raisedById" ON disputes ("raisedById");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_disputes_againstId" ON disputes ("againstId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_disputes_jobId" ON disputes ("jobId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_disputes_status" ON disputes (status);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_disputes_resolvedById" ON disputes ("resolvedById");`
    );

    // Create favorites table - matching Favorite entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS favorites (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "workerId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE("workerId", "jobId")
      );
    `);

    // Create indexes for favorites table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_favorites_workerId" ON favorites ("workerId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_favorites_jobId" ON favorites ("jobId");`
    );

    // Create trust_score_logs table - matching TrustScoreLog entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS trust_score_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "previousScore" INTEGER NOT NULL,
        "newScore" INTEGER NOT NULL,
        change INTEGER NOT NULL,
        reason TEXT NOT NULL,
        "relatedEntityType" VARCHAR(50),
        "relatedEntityId" VARCHAR(255),
        "adminId" UUID REFERENCES users(id),
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for trust_score_logs table
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_trust_score_logs_userId" ON trust_score_logs ("userId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_trust_score_logs_adminId" ON trust_score_logs ("adminId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_trust_score_logs_relatedEntity" ON trust_score_logs ("relatedEntityType", "relatedEntityId");`
    );

    // Create generic_chats table - matching GenericChat entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS generic_chats (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        type "chat_type_enum" NOT NULL DEFAULT 'general',
        title VARCHAR(255),
        description TEXT,
        "contextId" VARCHAR(255),
        "contextType" VARCHAR(50),
        metadata JSONB,
        "isActive" BOOLEAN DEFAULT TRUE,
        "createdBy" VARCHAR(255),
        "lastMessageAt" TIMESTAMP WITH TIME ZONE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create chat_participants table - matching ChatParticipant entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS chat_participants (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "chatId" UUID NOT NULL REFERENCES generic_chats(id) ON DELETE CASCADE,
        "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        role "participant_role_enum" DEFAULT 'member',
        "unreadCount" INTEGER DEFAULT 0,
        "isActive" BOOLEAN DEFAULT TRUE,
        "joinedAt" TIMESTAMP WITH TIME ZONE,
        "leftAt" TIMESTAMP WITH TIME ZONE,
        "lastReadAt" TIMESTAMP WITH TIME ZONE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE("chatId", "userId")
      );
    `);

    // Create chat_messages table - matching ChatMessage entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS chat_messages (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "genericChatId" UUID REFERENCES generic_chats(id) ON DELETE CASCADE,
        "messageType" "message_type_enum" DEFAULT 'text',
        message TEXT NOT NULL,
        "senderId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        status "message_status_enum" DEFAULT 'sent',
        "readAt" TIMESTAMP WITH TIME ZONE,
        metadata JSONB,
        "replyToId" UUID REFERENCES chat_messages(id),
        "editedAt" TIMESTAMP WITH TIME ZONE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for chat tables
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_generic_chats_type" ON generic_chats (type);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_generic_chats_contextId" ON generic_chats ("contextId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_generic_chats_contextType" ON generic_chats ("contextType");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_generic_chats_createdBy" ON generic_chats ("createdBy");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_chat_participants_chatId" ON chat_participants ("chatId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_chat_participants_userId" ON chat_participants ("userId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_chat_messages_genericChatId" ON chat_messages ("genericChatId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_chat_messages_senderId" ON chat_messages ("senderId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_chat_messages_createdAt" ON chat_messages ("createdAt");`
    );

    // Create badges table - matching Badge entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS badges (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT NOT NULL,
        icon VARCHAR(255) NOT NULL,
        category "badge_category_enum" NOT NULL,
        rarity "badge_rarity_enum" DEFAULT 'common',
        "requiredValue" INTEGER,
        "isActive" BOOLEAN DEFAULT TRUE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create user_badges table - matching UserBadge entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS user_badges (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "badgeId" UUID NOT NULL REFERENCES badges(id) ON DELETE CASCADE,
        "awardedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE("userId", "badgeId")
      );
    `);

    // Create indexes for badge tables
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_badges_category" ON badges (category);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_badges_name" ON badges (name);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_badges_rarity" ON badges (rarity);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_user_badges_userId" ON user_badges ("userId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_user_badges_badgeId" ON user_badges ("badgeId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_user_badges_awardedAt" ON user_badges ("awardedAt");`
    );

    // Create notifications table - matching Notification entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS notifications (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type VARCHAR(50) NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        "isRead" BOOLEAN DEFAULT FALSE,
        "readAt" TIMESTAMP WITH TIME ZONE,
        metadata JSONB,
        link VARCHAR(255),
        "isActionRequired" BOOLEAN DEFAULT FALSE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create activity_logs table - matching ActivityLog entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS activity_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "userId" UUID REFERENCES users(id) ON DELETE CASCADE,
        action VARCHAR(100) NOT NULL,
        "entityType" VARCHAR(50),
        "entityId" VARCHAR(255),
        description TEXT,
        metadata JSONB,
        "ipAddress" VARCHAR(45),
        "userAgent" TEXT,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create otps table - matching Otp entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS otps (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        email VARCHAR(255),
        phone VARCHAR(20),
        code VARCHAR(10) NOT NULL,
        "expiresAt" TIMESTAMP WITH TIME ZONE NOT NULL,
        verified BOOLEAN DEFAULT FALSE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        CHECK ((email IS NOT NULL AND phone IS NULL) OR (email IS NULL AND phone IS NOT NULL))
      );
    `);

    // Create settings table - matching Setting entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS settings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        key VARCHAR(255) NOT NULL UNIQUE,
        value TEXT NOT NULL,
        description TEXT,
        type VARCHAR(50) DEFAULT 'string',
        "isSystem" BOOLEAN DEFAULT FALSE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create job_templates table - matching JobTemplate entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS job_templates (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        duration INTEGER NOT NULL,
        "trustScoreRequired" INTEGER NOT NULL,
        "paymentAmount" DECIMAL(10, 2) NOT NULL,
        "requiresLaptop" BOOLEAN DEFAULT FALSE,
        "requiresSmartphone" BOOLEAN DEFAULT FALSE,
        "isEmergencyJob" BOOLEAN DEFAULT FALSE,
        "postedBy" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create escrow_accounts table - matching EscrowAccount entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS escrow_accounts (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
        "companyId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "workerId" UUID REFERENCES users(id),
        amount DECIMAL(10, 2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'INR',
        status "escrow_status_enum" DEFAULT 'pending',
        "paymentIntentId" VARCHAR(255),
        "transactionId" VARCHAR(255),
        description TEXT,
        metadata JSONB,
        "releasedAt" TIMESTAMP WITH TIME ZONE,
        "refundedAt" TIMESTAMP WITH TIME ZONE,
        "releasedById" UUID REFERENCES users(id),
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create password_reset_tokens table - matching PasswordResetToken entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS password_reset_tokens (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        email VARCHAR(255),
        phone VARCHAR(20),
        token VARCHAR(255) NOT NULL UNIQUE,
        "expiresAt" TIMESTAMP WITH TIME ZONE NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create reports table - matching Report entity structure
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS reports (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL,
        "dateRange" JSONB NOT NULL,
        filters JSONB,
        "includeCharts" BOOLEAN DEFAULT TRUE,
        "scheduledDelivery" BOOLEAN DEFAULT FALSE,
        "emailRecipients" VARCHAR(255),
        "lastGenerated" TIMESTAMP WITH TIME ZONE,
        "downloadUrl" VARCHAR(255),
        "createdBy" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create additional indexes
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_notifications_userId" ON notifications ("userId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_notifications_isRead" ON notifications ("isRead");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_notifications_type" ON notifications (type);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_activity_logs_userId" ON activity_logs ("userId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_activity_logs_action" ON activity_logs (action);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_activity_logs_entityType" ON activity_logs ("entityType");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_otps_email" ON otps (email);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_otps_phone" ON otps (phone);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_otps_expiresAt" ON otps ("expiresAt");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_settings_key" ON settings (key);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_settings_type" ON settings (type);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_job_templates_postedBy" ON job_templates ("postedBy");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_escrow_accounts_jobId" ON escrow_accounts ("jobId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_escrow_accounts_status" ON escrow_accounts (status);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_escrow_accounts_companyId" ON escrow_accounts ("companyId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_escrow_accounts_workerId" ON escrow_accounts ("workerId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_password_reset_tokens_userId" ON password_reset_tokens ("userId");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_password_reset_tokens_token" ON password_reset_tokens (token);`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_password_reset_tokens_expiresAt" ON password_reset_tokens ("expiresAt");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_reports_createdBy" ON reports ("createdBy");`
    );
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_reports_type" ON reports (type);`
    );
  }

  /**
   * Migration to revert the initial database schema
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop tables in reverse order to respect foreign key constraints
    await queryRunner.query(`DROP TABLE IF EXISTS reports;`);
    await queryRunner.query(`DROP TABLE IF EXISTS password_reset_tokens;`);
    await queryRunner.query(`DROP TABLE IF EXISTS escrow_accounts;`);
    await queryRunner.query(`DROP TABLE IF EXISTS job_templates;`);
    await queryRunner.query(`DROP TABLE IF EXISTS settings;`);
    await queryRunner.query(`DROP TABLE IF EXISTS otps;`);
    await queryRunner.query(`DROP TABLE IF EXISTS activity_logs;`);
    await queryRunner.query(`DROP TABLE IF EXISTS notifications;`);
    await queryRunner.query(`DROP TABLE IF EXISTS user_badges;`);
    await queryRunner.query(`DROP TABLE IF EXISTS badges;`);
    await queryRunner.query(`DROP TABLE IF EXISTS chat_messages;`);
    await queryRunner.query(`DROP TABLE IF EXISTS chat_participants;`);
    await queryRunner.query(`DROP TABLE IF EXISTS generic_chats;`);
    await queryRunner.query(`DROP TABLE IF EXISTS trust_score_logs;`);
    await queryRunner.query(`DROP TABLE IF EXISTS favorites;`);
    await queryRunner.query(`DROP TABLE IF EXISTS disputes;`);
    await queryRunner.query(`DROP TABLE IF EXISTS payouts;`);
    await queryRunner.query(`DROP TABLE IF EXISTS ratings;`);
    await queryRunner.query(`DROP TABLE IF EXISTS documents;`);
    await queryRunner.query(`DROP TABLE IF EXISTS applications;`);
    await queryRunner.query(`DROP TABLE IF EXISTS jobs;`);
    await queryRunner.query(`DROP TABLE IF EXISTS companies;`);
    await queryRunner.query(`DROP TABLE IF EXISTS users;`);

    // Drop enums in reverse order
    await queryRunner.query(`DROP TYPE IF EXISTS "report_status_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "escrow_status_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "message_status_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "message_type_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "participant_role_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "chat_type_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "badge_rarity_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "badge_category_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "verification_status_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "document_type_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "dispute_status_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "payout_status_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "application_status_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "job_status_enum";`);
    await queryRunner.query(`DROP TYPE IF EXISTS "user_role_enum";`);
  }
}
