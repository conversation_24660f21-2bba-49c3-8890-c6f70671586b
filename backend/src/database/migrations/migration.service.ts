import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource, QueryRunner } from "typeorm";
import { Migration } from "./migration.entity";
import fs from "fs";
import path from "path";

/**
 * Migration Service
 *
 * This service handles database migrations in a db-migrate style approach.
 * It provides methods for running and reverting migrations.
 */
@Injectable()
export class MigrationService {
  private readonly logger = new Logger(MigrationService.name);
  private readonly migrationsDir = path.join(__dirname, "files");

  constructor(
    @InjectRepository(Migration)
    private readonly migrationRepository: Repository<Migration>,
    private readonly dataSource: DataSource
  ) {}

  /**
   * Run all pending migrations
   */
  async runPendingMigrations(): Promise<void> {
    this.logger.log("Checking for pending migrations...");

    // Ensure migrations table exists
    await this.ensureMigrationsTableExists();

    // Get all migration files
    const migrationFiles = this.getMigrationFiles();

    // Get applied migrations from the database
    const appliedMigrations = await this.migrationRepository.find({
      where: { applied: true },
      order: { timestamp: "ASC" },
    });

    // Filter out migrations that have already been applied
    const pendingMigrations = migrationFiles.filter(
      (file) =>
        !appliedMigrations.some(
          (m) => m.name === this.getMigrationNameFromFile(file)
        )
    );

    if (pendingMigrations.length === 0) {
      this.logger.log("No pending migrations found.");
      return;
    }

    this.logger.log(`Found ${pendingMigrations.length} pending migrations.`);

    // Run each pending migration
    for (const migrationFile of pendingMigrations) {
      await this.runMigration(migrationFile);
    }

    this.logger.log("All migrations completed successfully.");
  }

  /**
   * Revert the last applied migration
   */
  async revertLastMigration(): Promise<void> {
    this.logger.log("Reverting last migration...");

    // Get the last applied migration
    const lastMigration = await this.migrationRepository.findOne({
      where: { applied: true },
      order: { timestamp: "DESC" },
    });

    if (!lastMigration) {
      this.logger.log("No migrations to revert.");
      return;
    }

    // Find the migration file
    const migrationFile = this.getMigrationFiles().find(
      (file) => this.getMigrationNameFromFile(file) === lastMigration.name
    );

    if (!migrationFile) {
      throw new Error(`Migration file for ${lastMigration.name} not found.`);
    }

    // Revert the migration
    await this.revertMigration(migrationFile);

    this.logger.log("Migration reverted successfully.");
  }

  /**
   * Run a specific migration
   * @param migrationName Name of the migration to run
   */
  async runMigrationByName(migrationName: string): Promise<void> {
    this.logger.log(`Running migration: ${migrationName}`);

    // Find the migration file
    const migrationFile = this.getMigrationFiles().find(
      (file) => this.getMigrationNameFromFile(file) === migrationName
    );

    if (!migrationFile) {
      throw new Error(`Migration file for ${migrationName} not found.`);
    }

    // Check if the migration has already been applied
    const existingMigration = await this.migrationRepository.findOne({
      where: { name: migrationName, applied: true },
    });

    if (existingMigration) {
      this.logger.log(`Migration ${migrationName} has already been applied.`);
      return;
    }

    // Run the migration
    await this.runMigration(migrationFile);

    this.logger.log(`Migration ${migrationName} completed successfully.`);
  }

  /**
   * Revert a specific migration
   * @param migrationName Name of the migration to revert
   */
  async revertMigrationByName(migrationName: string): Promise<void> {
    this.logger.log(`Reverting migration: ${migrationName}`);

    // Find the migration file
    const migrationFile = this.getMigrationFiles().find(
      (file) => this.getMigrationNameFromFile(file) === migrationName
    );

    if (!migrationFile) {
      throw new Error(`Migration file for ${migrationName} not found.`);
    }

    // Check if the migration has been applied
    const existingMigration = await this.migrationRepository.findOne({
      where: { name: migrationName, applied: true },
    });

    if (!existingMigration) {
      this.logger.log(`Migration ${migrationName} has not been applied.`);
      return;
    }

    // Revert the migration
    await this.revertMigration(migrationFile);

    this.logger.log(`Migration ${migrationName} reverted successfully.`);
  }

  /**
   * Get all migrations
   */
  async getAllMigrations(): Promise<{
    applied: Migration[];
    pending: string[];
  }> {
    // Ensure migrations table exists
    await this.ensureMigrationsTableExists();

    // Get all migration files
    const migrationFiles = this.getMigrationFiles();

    // Get applied migrations from the database
    const appliedMigrations = await this.migrationRepository.find({
      order: { timestamp: "ASC" },
    });

    // Get pending migrations
    const pendingMigrations = migrationFiles
      .filter(
        (file) =>
          !appliedMigrations.some(
            (m) => m.name === this.getMigrationNameFromFile(file)
          )
      )
      .map((file) => this.getMigrationNameFromFile(file));

    return {
      applied: appliedMigrations,
      pending: pendingMigrations,
    };
  }

  /**
   * Run a migration
   * @param migrationFile Path to the migration file
   */
  private async runMigration(migrationFile: string): Promise<void> {
    const migrationName = this.getMigrationNameFromFile(migrationFile);
    const timestamp = this.getTimestampFromName(migrationName);

    this.logger.log(`Running migration: ${migrationName}`);

    // Read the migration file
    const migration = require(migrationFile);

    // Create a query runner
    const queryRunner = this.dataSource.createQueryRunner();

    // Start a transaction
    await queryRunner.startTransaction();

    try {
      // Run the up method
      await migration.up(queryRunner);

      // Record the migration
      await this.recordMigration(queryRunner, migrationName, timestamp);

      // Commit the transaction
      await queryRunner.commitTransaction();

      this.logger.log(`Migration ${migrationName} completed successfully.`);
    } catch (error) {
      // Rollback the transaction
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Migration ${migrationName} failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Revert a migration
   * @param migrationFile Path to the migration file
   */
  private async revertMigration(migrationFile: string): Promise<void> {
    const migrationName = this.getMigrationNameFromFile(migrationFile);

    this.logger.log(`Reverting migration: ${migrationName}`);

    // Read the migration file
    const migration = require(migrationFile);

    // Create a query runner
    const queryRunner = this.dataSource.createQueryRunner();

    // Start a transaction
    await queryRunner.startTransaction();

    try {
      // Run the down method
      await migration.down(queryRunner);

      // Update the migration record
      await this.removeMigrationRecord(queryRunner, migrationName);

      // Commit the transaction
      await queryRunner.commitTransaction();

      this.logger.log(`Migration ${migrationName} reverted successfully.`);
    } catch (error) {
      // Rollback the transaction
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Migration ${migrationName} reversion failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Record a migration in the migrations table
   * @param queryRunner QueryRunner instance
   * @param migrationName Name of the migration
   * @param timestamp Timestamp of the migration
   */
  private async recordMigration(
    queryRunner: QueryRunner,
    migrationName: string,
    timestamp: number
  ): Promise<void> {
    await queryRunner.manager.insert(Migration, {
      name: migrationName,
      timestamp,
      applied: true,
    });
  }

  /**
   * Remove a migration record from the migrations table
   * @param queryRunner QueryRunner instance
   * @param migrationName Name of the migration
   */
  private async removeMigrationRecord(
    queryRunner: QueryRunner,
    migrationName: string
  ): Promise<void> {
    await queryRunner.manager.update(
      Migration,
      { name: migrationName },
      { applied: false }
    );
  }

  /**
   * Ensure the migrations table exists
   */
  private async ensureMigrationsTableExists(): Promise<void> {
    const tableExists = await this.dataSource.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'migrations'
      );
    `);

    if (!tableExists[0].exists) {
      this.logger.log("Creating migrations table...");

      await this.dataSource.query(`
        CREATE TABLE migrations (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) UNIQUE NOT NULL,
          timestamp BIGINT NOT NULL,
          applied BOOLEAN DEFAULT TRUE,
          executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);

      this.logger.log("Migrations table created.");
    }
  }

  /**
   * Get all migration files
   */
  private getMigrationFiles(): string[] {
    if (!fs.existsSync(this.migrationsDir)) {
      fs.mkdirSync(this.migrationsDir, { recursive: true });
      return [];
    }

    return fs
      .readdirSync(this.migrationsDir)
      .filter((file) => file.endsWith(".js") || file.endsWith(".ts"))
      .map((file) => path.join(this.migrationsDir, file))
      .sort();
  }

  /**
   * Get migration name from file path
   * @param filePath Path to the migration file
   */
  private getMigrationNameFromFile(filePath: string): string {
    return path.basename(filePath).replace(/\.(js|ts)$/, "");
  }

  /**
   * Get timestamp from migration name
   * @param migrationName Name of the migration
   */
  private getTimestampFromName(migrationName: string): number {
    const timestamp = migrationName.split("_")[0];
    return parseInt(timestamp, 10);
  }
}
