import { Enti<PERSON>, Column, PrimaryGeneratedColumn } from "typeorm";

/**
 * Migration Entity
 *
 * This entity represents a database migration record.
 * It is used to track which migrations have been applied to the database.
 */
@Entity("migrations")
export class Migration {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ unique: true })
  name!: string;

  @Column("bigint")
  timestamp!: number;

  @Column({ default: true })
  applied!: boolean;

  @Column({
    type: "timestamp with time zone",
    default: () => "CURRENT_TIMESTAMP",
  })
  executedAt!: Date;
}
