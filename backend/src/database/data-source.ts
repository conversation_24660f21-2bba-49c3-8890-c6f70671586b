import { DataSource, DataSourceOptions } from "typeorm";
import { config } from "dotenv";
import path from "path";

// Load environment variables from .env file
config({ path: path.resolve(process.cwd(), ".env") });

export const dataSourceOptions: DataSourceOptions = {
  type: "postgres",
  url:
    process.env.DATABASE_URL ||
    "postgres://postgres:@localhost:5432/job-platform",
  entities: [path.join(__dirname, "..", "**", "*.entity.{ts,js}")],
  migrations: [path.join(__dirname, "migrations/files", "*.{ts,js}")],
  synchronize: false,
  logging: process.env.NODE_ENV !== "production",
  ssl: process.env.DB_SSL === "true" ? { rejectUnauthorized: false } : false,
};

const dataSource = new DataSource(dataSourceOptions);
export default dataSource;
