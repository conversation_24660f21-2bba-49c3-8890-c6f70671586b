import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Job } from "../../jobs/entities/job.entity";
import { DisputeStatus } from "@shared/types";

@Entity("disputes")
export class Dispute {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column()
  @Index()
  raisedById!: string;

  @ManyToOne(() => User, (user) => user.raisedDisputes)
  @JoinColumn({ name: "raisedById" })
  raisedBy!: User;

  @Column()
  @Index()
  againstId!: string;

  @ManyToOne(() => User, (user) => user.receivedDisputes)
  @JoinColumn({ name: "againstId" })
  againstUser!: User;

  @Column()
  @Index()
  jobId!: string;

  @ManyToOne(() => Job)
  @JoinColumn({ name: "jobId" })
  job!: Job;

  @Column({ type: "text" })
  reason!: string;

  @Column({ type: "text", nullable: true })
  description?: string;

  @Column({
    type: "enum",
    enum: DisputeStatus,
    default: DisputeStatus.OPEN,
  })
  status!: DisputeStatus;

  @Column({ nullable: true })
  resolvedById?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: "resolvedById" })
  resolvedBy?: User;

  @Column({ nullable: true })
  resolvedAt?: Date;

  @Column({ type: "text", nullable: true })
  resolution?: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
