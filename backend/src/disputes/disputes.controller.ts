import {
  Controller,
  Get,
  Post,
  Patch,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  Inject,
  NotFoundException,
} from "@nestjs/common";
import { Request as ExpressRequest } from "express";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { DisputesService } from "./disputes.service";
import type { CreateDisputeDto, UpdateDisputeDto } from "@shared/validation";
import { type DisputeStatus, UserRole } from "@shared/types";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";

@ApiTags("disputes")
@Controller("disputes")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DisputesController {
  constructor(
    @Inject(DisputesService)
    private readonly disputesService: DisputesService
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new dispute" })
  @ApiResponse({ status: 201, description: "Dispute created successfully" })
  async create(
    @Request() req: ExpressRequest & { user: any },
    @Body() createDisputeDto: CreateDisputeDto
  ) {
    return this.disputesService.create(req.user.id, createDisputeDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all disputes" })
  @ApiResponse({ status: 200, description: "Return all disputes" })
  async findAll(
    @Request() req: ExpressRequest & { user: any },
    @Query("raisedById") raisedById?: string,
    @Query("againstId") againstId?: string,
    @Query("jobId") jobId?: string,
    @Query("status") status?: DisputeStatus
  ) {
    // If user is not an admin, they can only see disputes they're involved in
    if (req.user.role !== UserRole.ADMIN) {
      return this.disputesService.findAll({
        raisedById: req.user.id,
        againstId: req.user.id,
        ...(jobId && { jobId }),
        ...(status && { status }),
      });
    }

    return this.disputesService.findAll({
      ...(raisedById && { raisedById }),
      ...(againstId && { againstId }),
      ...(jobId && { jobId }),
      ...(status && { status }),
    });
  }

  @Get(":id")
  @ApiOperation({ summary: "Get dispute by ID" })
  @ApiResponse({ status: 200, description: "Return dispute by ID" })
  @ApiResponse({ status: 404, description: "Dispute not found" })
  async findOne(
    @Param("id") id: string,
    @Request() req: ExpressRequest & { user: any }
  ) {
    const dispute = await this.disputesService.findOne(id);

    // Check permissions
    if (
      req.user.role !== UserRole.ADMIN &&
      dispute.raisedById !== req.user.id &&
      dispute.againstId !== req.user.id
    ) {
      throw new Error("You can only view disputes you're involved in");
    }

    return dispute;
  }

  @Patch(":id")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update dispute (Admin only)" })
  @ApiResponse({ status: 200, description: "Dispute updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Dispute not found" })
  async update(
    @Param("id") id: string,
    @Body() updateDisputeDto: UpdateDisputeDto,
    @Request() req: ExpressRequest & { user: any }
  ) {
    return this.disputesService.update(
      id,
      req.user.id,
      req.user.role,
      updateDisputeDto
    );
  }

  @Get(":id/comments")
  @ApiOperation({ summary: "Get comments for a dispute" })
  @ApiResponse({ status: 200, description: "Return dispute comments" })
  @ApiResponse({ status: 404, description: "Dispute not found" })
  @ApiParam({ name: "id", description: "Dispute ID" })
  @ApiQuery({ name: "limit", required: false, type: Number })
  @ApiQuery({ name: "offset", required: false, type: Number })
  async getDisputeComments(
    @Param("id") id: string,
    @Request() req: ExpressRequest & { user: any },
    @Query("limit") limit: number = 10,
    @Query("offset") offset: number = 0
  ) {
    // Check if dispute exists and user has access
    const dispute = await this.disputesService.findOne(id);

    if (!dispute) {
      throw new NotFoundException(`Dispute with ID ${id} not found`);
    }

    // Check permissions
    if (
      req.user.role !== UserRole.ADMIN &&
      dispute.raisedById !== req.user.id &&
      dispute.againstId !== req.user.id
    ) {
      throw new Error("You can only view disputes you're involved in");
    }

    // This is a placeholder implementation
    // In a real application, you would query the database for comments
    return {
      comments: [
        {
          id: "1",
          disputeId: id,
          message: "This is a comment on the dispute",
          user: {
            id: "user1",
            name: "John Doe",
            profilePic: "https://example.com/profile.jpg",
          },
          createdAt: new Date().toISOString(),
        },
      ],
      total: 1,
      limit,
      offset,
    };
  }

  @Post(":id/comments")
  @ApiOperation({ summary: "Add a comment to a dispute" })
  @ApiResponse({ status: 201, description: "Comment added successfully" })
  @ApiResponse({ status: 404, description: "Dispute not found" })
  @ApiParam({ name: "id", description: "Dispute ID" })
  @ApiBody({
    description: "Comment data",
    schema: {
      type: "object",
      properties: {
        message: { type: "string", example: "This is a comment" },
      },
      required: ["message"],
    },
  })
  async addDisputeComment(
    @Param("id") id: string,
    @Body() data: { message: string },
    @Request() req: ExpressRequest & { user: any }
  ) {
    // Check if dispute exists and user has access
    const dispute = await this.disputesService.findOne(id);

    if (!dispute) {
      throw new NotFoundException(`Dispute with ID ${id} not found`);
    }

    // Check permissions
    if (
      req.user.role !== UserRole.ADMIN &&
      dispute.raisedById !== req.user.id &&
      dispute.againstId !== req.user.id
    ) {
      throw new Error("You can only comment on disputes you're involved in");
    }

    // This is a placeholder implementation
    // In a real application, you would save the comment to the database
    return {
      id: "new-comment-id",
      disputeId: id,
      message: data.message,
      user: {
        id: req.user.id,
        name: req.user.name || "User",
        profilePic: req.user.profilePic || "https://example.com/default.jpg",
      },
      createdAt: new Date().toISOString(),
    };
  }

  @Get("reasons")
  @ApiOperation({ summary: "Get dispute reasons" })
  @ApiResponse({ status: 200, description: "Return dispute reasons" })
  async getDisputeReasons() {
    // This is a placeholder implementation
    // In a real application, you would fetch this from the database
    return {
      reasons: [
        {
          id: "payment",
          name: "Payment Issue",
          description: "Issues related to payment for completed work",
        },
        {
          id: "quality",
          name: "Quality of Work",
          description: "Issues related to the quality of completed work",
        },
        {
          id: "behavior",
          name: "Unprofessional Behavior",
          description: "Issues related to unprofessional behavior",
        },
        {
          id: "communication",
          name: "Communication Problems",
          description: "Issues related to poor communication",
        },
        {
          id: "other",
          name: "Other",
          description: "Other issues not covered by the categories above",
        },
      ],
    };
  }
}
