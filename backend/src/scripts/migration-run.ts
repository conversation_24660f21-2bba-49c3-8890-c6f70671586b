import { NestFactory } from "@nestjs/core";
import { AppModule } from "../app.module";
import { MigrationService } from "../database/migrations/migration.service";
import { Logger } from "@nestjs/common";

/**
 * <PERSON>ript to run all pending migrations
 */
async function bootstrap() {
  const logger = new Logger("MigrationRun");

  try {
    logger.log("Starting migration process...");

    // Create NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);

    // Get migration service
    const migrationService = app.get(MigrationService);

    // Run migrations
    await migrationService.runPendingMigrations();

    // Close application
    await app.close();

    logger.log("Migration process completed successfully.");
    process.exit(0);
  } catch (error) {
    logger.error(`Migration process failed: ${(error as Error).message}`);
    process.exit(1);
  }
}

bootstrap();
