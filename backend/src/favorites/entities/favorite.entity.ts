import { Job } from "src/jobs/entities/job.entity";
import { User } from "src/users/entities/user.entity";
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  JoinColumn,
  Unique,
} from "typeorm";

@Entity("favorites")
@Unique(["workerId", "jobId"])
export class Favorite {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column()
  workerId!: string;

  @ManyToOne(() => User, (user) => user.favorites)
  @JoinColumn({ name: "workerId" })
  worker!: User;

  @Column()
  jobId!: string;

  @ManyToOne(() => Job, (job) => job.favorites)
  @JoinColumn({ name: "jobId" })
  job!: Job;

  @CreateDateColumn()
  createdAt!: Date;
}
