import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Job } from "../../jobs/entities/job.entity";

@Entity("ratings")
@Unique(["ratedById", "ratedUserId", "jobId"])
export class Rating {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column()
  @Index()
  ratedById!: string;

  @ManyToOne(() => User, (user) => user.givenRatings)
  @JoinColumn({ name: "ratedById" })
  ratedBy!: User;

  @Column()
  @Index()
  ratedUserId!: string;

  @ManyToOne(() => User, (user) => user.receivedRatings)
  @JoinColumn({ name: "ratedUserId" })
  ratedUser!: User;

  @Column()
  @Index()
  jobId!: string;

  @ManyToOne(() => Job)
  @JoinColumn({ name: "jobId" })
  job!: Job;

  @Column({ type: "int" })
  stars!: number;

  @Column({ type: "text", nullable: true })
  comment?: string;

  @Column({ default: false })
  isAnonymous!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
