import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Rating } from "./entities/rating.entity";
import { UsersService } from "../users/users.service";
import { JobsService } from "../jobs/jobs.service";
import { NotificationsService } from "../notifications/notifications.service";
import { ActivityLogService } from "../activity-log/activity-log.service";
import type { CreateRatingDto } from "@shared/validation";

@Injectable()
export class RatingsService {
  constructor(
    @InjectRepository(Rating)
    private ratingsRepository: Repository<Rating>,
    private usersService: UsersService,
    private jobsService: JobsService,
    private notificationsService: NotificationsService,
    private activityLogService: ActivityLogService
  ) {}

  async create(
    createRatingDto: CreateRatingDto,
    userId: string
  ): Promise<Rating> {
    // Validate that the user hasn't already rated for this job
    const existingRating = await this.ratingsRepository.findOne({
      where: {
        ratedById: userId,
        jobId: createRatingDto.jobId,
        ratedUserId: createRatingDto.toId,
      },
    });

    if (existingRating) {
      throw new BadRequestException(
        "You have already rated this user for this job"
      );
    }

    // Validate the job exists
    const job = await this.jobsService.findOne(createRatingDto.jobId);
    if (!job) {
      throw new NotFoundException("Job not found");
    }

    // Validate the user being rated exists
    const ratedUser = await this.usersService.findOne(createRatingDto.toId);
    if (!ratedUser) {
      throw new NotFoundException("User to be rated not found");
    }

    // Create the rating with correct property names
    const ratingData: Partial<Rating> = {
      ratedById: userId,
      ratedUserId: createRatingDto.toId,
      jobId: createRatingDto.jobId,
      stars: createRatingDto.stars,
    };

    if (createRatingDto.feedback) {
      ratingData.comment = createRatingDto.feedback;
    }

    const rating = this.ratingsRepository.create(ratingData);
    const savedRating = await this.ratingsRepository.save(rating);

    // Send notification to the rated user
    await this.notificationsService.create({
      userId: createRatingDto.toId,
      type: "RATING_RECEIVED",
      title: "New Rating Received",
      message: `You received a ${rating.stars}-star rating for job: ${job.title}`,
      metadata: { ratingId: savedRating.id, jobId: job.id },
    });

    // Log the activity
    await this.activityLogService.logActivity({
      userId,
      action: "RATING_CREATED",
      entityType: "RATING",
      entityId: savedRating.id,
      description: `Rated user ${createRatingDto.toId} with ${rating.stars} stars for job ${job.title}`,
    });

    return savedRating;
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    filters?: any
  ): Promise<{ data: Rating[]; total: number; page: number; limit: number }> {
    const queryBuilder = this.ratingsRepository
      .createQueryBuilder("rating")
      .leftJoinAndSelect("rating.ratedBy", "ratedBy")
      .leftJoinAndSelect("rating.ratedUser", "ratedUser")
      .leftJoinAndSelect("rating.job", "job")
      .orderBy("rating.createdAt", "DESC");

    if (filters) {
      if (filters.ratedById) {
        queryBuilder.andWhere("rating.ratedById = :ratedById", {
          ratedById: filters.ratedById,
        });
      }
      if (filters.ratedUserId) {
        queryBuilder.andWhere("rating.ratedUserId = :ratedUserId", {
          ratedUserId: filters.ratedUserId,
        });
      }
      if (filters.jobId) {
        queryBuilder.andWhere("rating.jobId = :jobId", {
          jobId: filters.jobId,
        });
      }
      if (filters.minStars) {
        queryBuilder.andWhere("rating.stars >= :minStars", {
          minStars: filters.minStars,
        });
      }
      if (filters.maxStars) {
        queryBuilder.andWhere("rating.stars <= :maxStars", {
          maxStars: filters.maxStars,
        });
      }
    }

    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      data,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<Rating> {
    const rating = await this.ratingsRepository.findOne({
      where: { id },
      relations: ["ratedBy", "ratedUser", "job"],
    });

    if (!rating) {
      throw new NotFoundException(`Rating with ID ${id} not found`);
    }

    return rating;
  }

  async findByUser(userId: string): Promise<Rating[]> {
    return this.ratingsRepository.find({
      where: [{ ratedUserId: userId }, { ratedById: userId }],
      relations: ["ratedBy", "ratedUser", "job"],
      order: { createdAt: "DESC" },
    });
  }

  async findByJob(jobId: string): Promise<Rating[]> {
    return this.ratingsRepository.find({
      where: { jobId },
      relations: ["ratedBy", "ratedUser", "job"],
      order: { createdAt: "DESC" },
    });
  }

  async getUserAverageRating(
    userId: string
  ): Promise<{ average: number; count: number }> {
    const result = await this.ratingsRepository
      .createQueryBuilder("rating")
      .select("AVG(rating.stars)", "average")
      .addSelect("COUNT(rating.id)", "count")
      .where("rating.ratedUserId = :userId", { userId })
      .getRawOne();

    return {
      average: result.average ? parseFloat(result.average) : 0,
      count: parseInt(result.count, 10),
    };
  }
}
