import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsString,
  IsInt,
  IsBoolean,
  IsOptional,
  Min,
  Max,
  Length,
  IsUUID,
} from "class-validator";

export class CreateRatingDto {
  @ApiProperty({ description: "User ID being rated" })
  @IsUUID()
  ratedUserId!: string;

  @ApiProperty({ description: "Job ID associated with this rating" })
  @IsUUID()
  jobId!: string;

  @ApiProperty({ description: "Rating stars (1-5)", minimum: 1, maximum: 5 })
  @IsInt()
  @Min(1)
  @Max(5)
  stars!: number;

  @ApiPropertyOptional({ description: "Rating comment or feedback" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  comment?: string;

  @ApiPropertyOptional({
    description: "Should this rating be anonymous?",
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isAnonymous?: boolean = false;
}
