import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from "typeorm";
import { User } from "src/users/entities/user.entity";

@Entity("reports")
export class Report {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column()
  name!: string;

  @Column()
  type!: string;

  @Column("jsonb")
  dateRange!: {
    from: Date;
    to: Date;
  };

  @Column("jsonb", { nullable: true })
  filters?: string[];

  @Column({ default: true })
  includeCharts!: boolean;

  @Column({ default: false })
  scheduledDelivery!: boolean;

  @Column({ nullable: true })
  emailRecipients?: string;

  @Column({ nullable: true })
  lastGenerated?: Date;

  @Column({ nullable: true })
  downloadUrl?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: "createdBy" })
  createdByUser!: User;

  @Column()
  createdBy!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
