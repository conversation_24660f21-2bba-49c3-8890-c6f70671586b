import { Transform } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class Worker {
  @ApiProperty({
    description: "KYC verification status",
    default: false,
  })
  isKycVerified!: boolean;

  @ApiPropertyOptional({ description: "Date of birth" })
  @Transform(({ value }: { value: any }) => value && new Date(value))
  dateOfBirth?: Date;

  @ApiPropertyOptional({ description: "Skills (comma separated)" })
  skills?: string;

  @ApiPropertyOptional({ description: "Education background" })
  education?: string;

  @ApiPropertyOptional({ description: "Work experience" })
  experience?: string;

  @ApiPropertyOptional({ description: "Languages (comma separated)" })
  languages?: string;
}
