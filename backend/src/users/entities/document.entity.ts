import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { Transform } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { User } from "./user.entity";
import { DocumentType } from "../../common/enums/document-type.enum";
import { VerificationStatus } from "../../common/enums/verification-status.enum";

@Entity("documents")
export class Document {
  @ApiProperty({ description: "Unique identifier" })
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @ApiPropertyOptional({ description: "User ID" })
  @Column({ nullable: true })
  userId!: string;

  @ManyToOne(() => User, (user) => user.documents, { nullable: true })
  @JoinColumn({ name: "userId" })
  user!: User;

  @ApiProperty({
    description: "Document type",
    enum: DocumentType,
  })
  @Column({
    type: "enum",
    enum: DocumentType,
  })
  documentType!: DocumentType;

  @ApiProperty({ description: "Document URL" })
  @Column()
  documentUrl!: string;

  @ApiPropertyOptional({ description: "Document number (e.g., ID number)" })
  @Column({ nullable: true })
  documentNumber?: string;

  @ApiProperty({
    description: "Verification status",
    enum: VerificationStatus,
    default: VerificationStatus.PENDING,
  })
  @Column({
    type: "enum",
    enum: VerificationStatus,
    default: VerificationStatus.PENDING,
  })
  verificationStatus!: VerificationStatus;

  @ApiPropertyOptional({ description: "ID of admin who verified the document" })
  @Column({ nullable: true })
  verifiedBy?: string;

  @ApiPropertyOptional({ description: "Verification date" })
  @Column({ nullable: true })
  @Transform(({ value }) => value && new Date(value))
  verifiedAt?: Date;

  @ApiPropertyOptional({ description: "Reason for rejection" })
  @Column({ nullable: true })
  rejectionReason?: string;

  @ApiProperty({ description: "Creation date" })
  @CreateDateColumn()
  @Transform(({ value }) => value && new Date(value))
  createdAt!: Date;

  @ApiProperty({ description: "Last update date" })
  @UpdateDateColumn()
  @Transform(({ value }) => value && new Date(value))
  updatedAt!: Date;
}
