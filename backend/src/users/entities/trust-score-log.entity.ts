import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { Transform } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { User } from "./user.entity";

@Entity("trust_score_logs")
export class TrustScoreLog {
  @ApiProperty({ description: "Unique identifier" })
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @ApiProperty({ description: "User ID" })
  @Column()
  userId!: string;

  @ManyToOne(() => User, (user: any) => user.trustScoreLogs)
  @JoinColumn({ name: "userId" })
  user!: User;

  @ApiProperty({ description: "Previous trust score" })
  @Column()
  previousScore!: number;

  @ApiProperty({ description: "New trust score" })
  @Column()
  newScore!: number;

  @ApiProperty({ description: "Score change amount" })
  @Column()
  change!: number;

  @ApiProperty({ description: "Reason for score change" })
  @Column()
  reason!: string;

  @ApiPropertyOptional({
    description: "Related entity type (e.g., 'job', 'application')",
  })
  @Column({ nullable: true })
  relatedEntityType?: string;

  @ApiPropertyOptional({ description: "Related entity ID" })
  @Column({ nullable: true })
  relatedEntityId?: string;

  @ApiPropertyOptional({ description: "Admin ID who made the change" })
  @Column({ nullable: true })
  adminId?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: "adminId" })
  admin?: User;

  @ApiProperty({ description: "Creation date" })
  @CreateDateColumn()
  @Transform(({ value }: { value: any }) => value && new Date(value))
  createdAt!: Date;
}
