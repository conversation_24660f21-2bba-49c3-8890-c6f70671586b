import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsString, ValidateIf } from "class-validator";
import { VerificationStatus } from "../../common/enums/verification-status.enum";

export class VerifyDocumentDto {
  @ApiProperty({
    description: "Verification status",
    enum: VerificationStatus,
    example: VerificationStatus.VERIFIED,
  })
  @IsEnum(VerificationStatus)
  status!: VerificationStatus;

  @ApiPropertyOptional({
    description: "Reason for rejection (required if status is REJECTED)",
    example: "Document is illegible",
  })
  @ValidateIf((o) => o.status === VerificationStatus.REJECTED)
  @IsString()
  rejectionReason?: string;
}
