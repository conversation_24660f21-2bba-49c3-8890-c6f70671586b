import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsString,
  IsOptional,
  IsDate,
  Length,
  IsISO31661Alpha2,
} from "class-validator";
import { Type } from "class-transformer";

export class UpdateKycDto {
  @ApiProperty({
    description: "User's full legal name",
    example: "<PERSON>",
  })
  @IsString()
  @Length(2, 100)
  fullName!: string;

  @ApiProperty({
    description: "User's date of birth",
    example: "1990-01-01",
  })
  @IsDate()
  @Type(() => Date)
  dateOfBirth!: Date;

  @ApiProperty({
    description: "User's street address",
    example: "123 Main St, Apt 4B",
  })
  @IsString()
  address!: string;

  @ApiProperty({
    description: "User's city",
    example: "New York",
  })
  @IsString()
  city!: string;

  @ApiProperty({
    description: "User's state or province",
    example: "NY",
  })
  @IsString()
  state!: string;

  @ApiProperty({
    description: "User's postal code",
    example: "10001",
  })
  @IsString()
  postalCode!: string;

  @ApiProperty({
    description: "User's country (ISO 3166-1 alpha-2)",
    example: "US",
  })
  @IsISO31661Alpha2()
  country!: string;

  @ApiPropertyOptional({
    description: "Additional notes or information",
    example: "Previously verified with ABC Company",
  })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  notes?: string;
}
