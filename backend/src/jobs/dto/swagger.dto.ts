import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { JobStatus } from "../../common/enums/job-status.enum";

/**
 * Swagger DTO for Job response
 */
export class JobResponseSwaggerDto {
  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440000" })
  id!: string;

  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440001" })
  companyId!: string;

  @ApiProperty({ example: "Inventory Specialist" })
  title!: string;

  @ApiProperty({
    example:
      "We are looking for an inventory specialist to help with our warehouse operations...",
  })
  description!: string;

  @ApiProperty({ example: "123 Main St, Suite 200" })
  location!: string;

  @ApiPropertyOptional({ example: "San Francisco" })
  city?: string;

  @ApiPropertyOptional({ example: "CA" })
  state?: string;

  @ApiPropertyOptional({ example: "USA" })
  country?: string;

  @ApiPropertyOptional({ example: 37.7749 })
  latitude?: number;

  @ApiPropertyOptional({ example: -122.4194 })
  longitude?: number;

  @ApiProperty({ example: "2023-06-15T09:00:00Z" })
  startDateTime!: Date;

  @ApiProperty({ example: "2023-06-15T17:00:00Z" })
  endDateTime!: Date;

  @ApiProperty({ example: 25.0 })
  payRate!: number;

  @ApiProperty({ example: "hourly" })
  payRateType!: string;

  @ApiProperty({ example: 50 })
  trustScoreRequired!: number;

  @ApiProperty({ example: false })
  isEmergencyJob!: boolean;

  @ApiProperty({ example: true })
  requiresLaptop!: boolean;

  @ApiProperty({ example: true })
  requiresSmartphone!: boolean;

  @ApiPropertyOptional({ example: "Excel, Inventory Management" })
  requiredSkills?: string;

  @ApiPropertyOptional({ example: "1+ years in inventory management" })
  requiredExperience?: string;

  @ApiPropertyOptional({ example: "High School Diploma" })
  requiredEducation?: string;

  @ApiPropertyOptional({ example: "English, Spanish" })
  requiredLanguages?: string;

  @ApiPropertyOptional({ example: "Must be able to lift 25 lbs" })
  additionalRequirements?: string;

  @ApiProperty({ enum: JobStatus, example: JobStatus.OPEN })
  status!: JobStatus;

  @ApiPropertyOptional({ example: null })
  cancelledReason?: string;

  @ApiPropertyOptional({ example: null })
  cancelledAt?: Date;

  @ApiProperty({ example: 2 })
  maxPositions!: number;

  @ApiProperty({ example: 0 })
  filledPositions!: number;

  @ApiProperty({ example: "2023-06-01T12:00:00Z" })
  createdAt!: Date;

  @ApiProperty({ example: "2023-06-01T12:00:00Z" })
  updatedAt!: Date;
}

/**
 * Swagger DTO for Jobs list response
 */
export class JobsListResponseSwaggerDto {
  @ApiProperty({ type: [JobResponseSwaggerDto] })
  jobs!: JobResponseSwaggerDto[];

  @ApiProperty({ example: 100 })
  total!: number;

  @ApiProperty({ example: 10 })
  limit!: number;

  @ApiProperty({ example: 0 })
  offset!: number;
}

/**
 * Swagger DTO for Job application stats response
 */
export class JobApplicationStatsSwaggerDto {
  @ApiProperty({ example: 25 })
  total!: number;

  @ApiProperty({ example: 10 })
  pending!: number;

  @ApiProperty({ example: 5 })
  shortlisted!: number;

  @ApiProperty({ example: 3 })
  rejected!: number;

  @ApiProperty({ example: 5 })
  hired!: number;

  @ApiProperty({ example: 2 })
  withdrawn!: number;
}
