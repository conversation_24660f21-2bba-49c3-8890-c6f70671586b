import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsString,
  IsNumber,
  IsBoolean,
  IsDate,
  IsOptional,
  Min,
  Max,
  Length,
  IsEnum,
  IsLatitude,
  IsLongitude,
  IsPositive,
  isObject,
  IsObject,
} from "class-validator";
import { Type } from "class-transformer";
import { AdditionalRequirementsDTO } from "../entities/job.entity";

export class CreateJobDto {
  @ApiProperty({
    description: "Job title",
    example: "Inventory Specialist for Retail Store",
  })
  @IsString()
  @Length(3, 100)
  title!: string;

  @ApiProperty({
    description: "Job description",
    example:
      "We are looking for an experienced inventory specialist to conduct a full stock audit...",
  })
  @IsString()
  @Length(10, 5000)
  description!: string;

  @ApiProperty({
    description: "Job location (address)",
    example: "123 Main Street, New York, NY 10001",
  })
  @IsString()
  location!: string;

  @ApiPropertyOptional({
    description: "City",
    example: "New York",
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({
    description: "State/Province",
    example: "NY",
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({
    description: "Country",
    example: "US",
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({
    description: "Latitude coordinate",
    example: 40.7128,
  })
  @IsOptional()
  @IsLatitude()
  latitude?: number;

  @ApiPropertyOptional({
    description: "Longitude coordinate",
    example: -74.006,
  })
  @IsOptional()
  @IsLongitude()
  longitude?: number;

  @ApiProperty({
    description: "Job start date and time",
    example: "2023-12-01T09:00:00Z",
  })
  @IsDate()
  @Type(() => Date)
  startDateTime!: Date;

  @ApiProperty({
    description: "Job end date and time",
    example: "2023-12-01T17:00:00Z",
  })
  @IsDate()
  @Type(() => Date)
  endDateTime!: Date;

  @ApiProperty({
    description: "Pay rate amount",
    example: 25.5,
  })
  @IsNumber()
  @IsPositive()
  payRate!: number;

  @ApiPropertyOptional({
    description: "Pay rate type (hourly, daily, fixed)",
    default: "hourly",
    example: "hourly",
  })
  @IsOptional()
  @IsString()
  @IsEnum(["hourly", "daily", "fixed"])
  payRateType?: string = "hourly";

  @ApiPropertyOptional({
    description: "Minimum trust score required",
    default: 0,
    minimum: 0,
    maximum: 100,
    example: 70,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  trustScoreRequired?: number = 0;

  @ApiPropertyOptional({
    description: "Is this an emergency job?",
    default: false,
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isEmergencyJob?: boolean = false;

  @ApiPropertyOptional({
    description: "Does this job require a laptop?",
    default: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  requiresLaptop?: boolean = false;

  @ApiPropertyOptional({
    description: "Does this job require a smartphone?",
    default: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  requiresSmartphone?: boolean = false;

  @ApiPropertyOptional({
    description: "Additional requirements",
    example: "Must be able to lift up to 25 pounds",
    type: AdditionalRequirementsDTO,
  })
  @IsOptional()
  @IsObject()
  additionalRequirements?: AdditionalRequirementsDTO;

  @ApiPropertyOptional({
    description: "Maximum number of positions to fill",
    default: 1,
    minimum: 1,
    example: 3,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxPositions?: number = 1;
}
