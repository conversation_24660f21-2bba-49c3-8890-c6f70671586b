import { Injectable, Inject, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { Notification } from "./entities/notification.entity";
import { PushNotificationService } from "./push-notification.service";

interface CreateNotificationParams {
  userId: string;
  title: string;
  message: string;
  type: string;
  metadata?: Record<string, any>;
  link?: string;
  sendPush?: boolean;
  imageUrl?: string;
}

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    @InjectRepository(Notification)
    private notificationsRepository: Repository<Notification>,
    @Inject(PushNotificationService)
    private pushNotificationService: PushNotificationService
  ) {}

  async create({
    userId,
    title,
    message,
    type,
    metadata,
    link,
    sendPush = true,
    imageUrl,
  }: CreateNotificationParams): Promise<Notification> {
    // Create database notification
    const notificationData: any = {
      userId,
      title,
      message,
      type,
      isRead: false,
    };

    if (metadata) {
      notificationData.metadata = metadata;
    }

    if (link) {
      notificationData.link = link;
    }

    const notification = this.notificationsRepository.create(notificationData);

    const savedNotification = (await this.notificationsRepository.save(
      notification
    )) as unknown as Notification;

    // Send push notification if enabled
    if (sendPush) {
      try {
        const pushPayload: any = {
          title,
          body: message,
          data: {
            notificationId: savedNotification.id,
            type,
            link: link || "",
            ...metadata,
          },
        };

        if (imageUrl) {
          pushPayload.imageUrl = imageUrl;
        }

        await this.pushNotificationService.sendToUser(userId, pushPayload);
      } catch (error) {
        this.logger.error(
          `Failed to send push notification to user ${userId}:`,
          error
        );
        // Don't fail the whole operation if push notification fails
      }
    }

    return savedNotification;
  }

  async findAllForUser(
    userId: string,
    isRead?: boolean
  ): Promise<Notification[]> {
    const query: any = { userId };

    if (isRead !== undefined) {
      query.isRead = isRead;
    }

    return this.notificationsRepository.find({
      where: query,
      order: { createdAt: "DESC" },
    });
  }

  async markAsRead(id: string, userId: string): Promise<Notification> {
    const notification = await this.notificationsRepository.findOne({
      where: { id, userId },
    });

    if (!notification) {
      throw new Error(`Notification with ID ${id} not found`);
    }

    notification.isRead = true;
    notification.readAt = new Date();

    return this.notificationsRepository.save(notification);
  }

  async markAllAsRead(userId: string): Promise<void> {
    await this.notificationsRepository.update(
      { userId, isRead: false },
      { isRead: true, readAt: new Date() }
    );
  }

  async getUnreadCount(userId: string): Promise<number> {
    return this.notificationsRepository.count({
      where: { userId, isRead: false },
    });
  }

  async deleteNotification(id: string): Promise<void> {
    const notification = await this.notificationsRepository.findOne({
      where: { id },
    });

    if (!notification) {
      throw new Error(`Notification with ID ${id} not found`);
    }

    await this.notificationsRepository.remove(notification);
  }
}
