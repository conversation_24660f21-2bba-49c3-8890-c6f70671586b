import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsString,
  IsBoolean,
  IsOptional,
  IsObject,
  IsUUID,
  Length,
} from "class-validator";

export class CreateNotificationDto {
  @ApiProperty({ description: "User ID to send notification to" })
  @IsUUID()
  userId!: string;

  @ApiProperty({ description: "Notification title" })
  @IsString()
  @Length(1, 100)
  title!: string;

  @ApiProperty({ description: "Notification message" })
  @IsString()
  @Length(1, 1000)
  message!: string;

  @ApiProperty({
    description: "Notification type (e.g., 'job', 'application', 'payment')",
  })
  @IsString()
  type!: string;

  @ApiPropertyOptional({ description: "Additional metadata as JSON" })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: "Link to redirect when notification is clicked",
  })
  @IsOptional()
  @IsString()
  link?: string;

  @ApiPropertyOptional({
    description: "Does this notification require action?",
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isActionRequired?: boolean = false;
}
