import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  Index,
} from "typeorm";
import { GenericChat } from "./generic-chat.entity";
import { User } from "src/users/entities/user.entity";
import { ParticipantRole } from "../enums/chat-type.enum";

@Entity("chat_participants")
@Index(["chatId", "userId"], { unique: true })
export class ChatParticipant {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @ManyToOne(() => GenericChat, (chat) => chat.participants, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "chatId" })
  chat!: GenericChat;

  @Column()
  @Index()
  chatId!: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: "userId" })
  user!: User;

  @Column()
  @Index()
  userId!: string;

  @Column({
    type: "enum",
    enum: ParticipantRole,
    default: ParticipantRole.MEMBER,
  })
  role!: ParticipantRole;

  @Column({ default: 0 })
  unreadCount!: number;

  @Column({ default: true })
  isActive!: boolean;

  @Column({ nullable: true })
  joinedAt?: Date;

  @Column({ nullable: true })
  leftAt?: Date;

  @Column({ nullable: true })
  lastReadAt?: Date;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
