import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { JwtModule } from "@nestjs/jwt";
import { ChatMessage } from "./entities/chat-message.entity";
import { GenericChat } from "./entities/generic-chat.entity";
import { ChatParticipant } from "./entities/chat-participant.entity";
import { GenericChatController } from "./generic-chat.controller";
import { GenericChatService } from "./generic-chat.service";
import { ChatGateway } from "./chat.gateway";
import { NotificationsModule } from "../notifications/notifications.module";
import { WsJwtGuard } from "../auth/guards/ws-jwt.guard";

@Module({
  imports: [
    TypeOrmModule.forFeature([ChatMessage, GenericChat, ChatParticipant]),
    NotificationsModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || "default-secret",
      signOptions: { expiresIn: "24h" },
    }),
  ],
  controllers: [GenericChatController],
  providers: [GenericChatService, ChatGateway, WsJwtGuard],
  exports: [GenericChatService, ChatGateway],
})
export class ChatModule {}
