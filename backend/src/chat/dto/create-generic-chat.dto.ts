import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsUUID,
  IsOptional,
  IsString,
  IsEnum,
  IsArray,
  ValidateNested,
  IsObject,
} from "class-validator";
import { Type } from "class-transformer";
import { ChatType, ParticipantRole } from "../enums/chat-type.enum";

export class ChatParticipantDto {
  @ApiProperty({
    description: "User ID of the participant",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @IsUUID()
  userId!: string;

  @ApiProperty({
    description: "Role of the participant in the chat",
    enum: ParticipantRole,
    example: ParticipantRole.MEMBER,
  })
  @IsEnum(ParticipantRole)
  role!: ParticipantRole;
}

export class CreateGenericChatDto {
  @ApiProperty({
    description: "Type of chat",
    enum: ChatType,
    example: ChatType.GENERAL,
  })
  @IsEnum(ChatType)
  type!: ChatType;

  @ApiPropertyOptional({
    description: "Title of the chat (optional for group chats)",
    example: "Project Discussion",
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: "Description of the chat",
    example: "Discussion about the new project requirements",
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: "Context ID (e.g., jobId, supportTicketId)",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @IsOptional()
  @IsString()
  contextId?: string;

  @ApiPropertyOptional({
    description: "Type of context (e.g., 'job', 'support_ticket')",
    example: "job",
  })
  @IsOptional()
  @IsString()
  contextType?: string;

  @ApiProperty({
    description: "List of participants to add to the chat",
    type: [ChatParticipantDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatParticipantDto)
  participants!: ChatParticipantDto[];

  @ApiPropertyOptional({
    description: "Additional metadata for the chat",
    example: { priority: "high", department: "engineering" },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
