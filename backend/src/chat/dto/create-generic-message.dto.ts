import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsString,
  IsNotEmpty,
  MaxLength,
  IsEnum,
  IsOptional,
  IsUUID,
  IsObject,
} from "class-validator";
import { MessageType } from "../enums/chat-type.enum";

export class CreateGenericMessageDto {
  @ApiProperty({
    description: "Message content",
    example: "Hello, I'm interested in your job posting.",
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(2000)
  message!: string;

  @ApiProperty({
    description: "Type of message",
    enum: MessageType,
    example: MessageType.TEXT,
  })
  @IsEnum(MessageType)
  messageType: MessageType = MessageType.TEXT;

  @ApiPropertyOptional({
    description: "File URL for image/file messages",
    example: "https://example.com/files/document.pdf",
  })
  @IsOptional()
  @IsString()
  fileUrl?: string;

  @ApiPropertyOptional({
    description: "Original file name",
    example: "document.pdf",
  })
  @IsOptional()
  @IsString()
  fileName?: string;

  @ApiPropertyOptional({
    description: "File size in bytes",
    example: 1024000,
  })
  @IsOptional()
  fileSize?: number;

  @ApiPropertyOptional({
    description: "File MIME type",
    example: "application/pdf",
  })
  @IsOptional()
  @IsString()
  fileMimeType?: string;

  @ApiPropertyOptional({
    description: "ID of message being replied to",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @IsOptional()
  @IsUUID()
  replyToId?: string;

  @ApiPropertyOptional({
    description: "Additional metadata for the message",
    example: { priority: "high", tags: ["urgent"] },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
