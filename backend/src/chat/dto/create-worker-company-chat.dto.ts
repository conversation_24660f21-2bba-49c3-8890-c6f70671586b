import { IsString, IsOptional, IsUUID } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class CreateWorkerCompanyChatDto {
  @ApiProperty({
    description: "Worker user ID",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @IsUUID()
  workerId!: string;

  @ApiProperty({
    description: "Company user ID",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @IsUUID()
  companyId!: string;

  @ApiPropertyOptional({
    description: "Job ID if this chat is related to a specific job",
    example: "123e4567-e89b-12d3-a456-************",
  })
  @IsOptional()
  @IsUUID()
  jobId?: string;

  @ApiPropertyOptional({
    description: "Initial message to send when creating the chat",
    example: "Hello, I'm interested in this job opportunity.",
  })
  @IsOptional()
  @IsString()
  initialMessage?: string;

  @ApiPropertyOptional({
    description: "Chat title",
    example: "Job Application Discussion",
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: "Chat description",
    example: "Discussion about the warehouse job application",
  })
  @IsOptional()
  @IsString()
  description?: string;
}
