import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import nodemailer from "nodemailer";

interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  attachments?: Array<{
    filename: string;
    content: string | Buffer;
    contentType?: string;
  }>;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter!: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const emailService = this.configService.get<string>("EMAIL_SERVICE");
    const emailHost = this.configService.get<string>("EMAIL_HOST");
    const emailPort = this.configService.get<number>("EMAIL_PORT");
    const emailUser = this.configService.get<string>("EMAIL_USER");
    const emailPassword = this.configService.get<string>("EMAIL_PASSWORD");

    if (!emailHost || !emailPort || !emailUser || !emailPassword) {
      this.logger.warn(
        "Email configuration is incomplete. Email sending will be disabled."
      );
      return;
    }

    try {
      this.transporter = nodemailer.createTransport({
        service: emailService,
        host: emailHost,
        port: emailPort,
        secure: emailPort === 465, // true for 465, false for other ports
        auth: {
          user: emailUser,
          pass: emailPassword,
        },
      });

      this.logger.log("Email transporter initialized successfully");
    } catch (error) {
      this.logger.error("Failed to initialize email transporter:", error);
    }
  }

  /**
   * Send an email
   * @param options Email options
   * @returns Promise resolving to success status
   */
  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn(
        "Email transporter not initialized. Email will not be sent."
      );
      return false;
    }

    try {
      const defaultFrom = this.configService.get<string>("EMAIL_FROM");

      const mailOptions = {
        from: options.from || defaultFrom,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
        attachments: options.attachments,
      };

      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully to ${options.to}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email to ${options.to}:`, error);
      return false;
    }
  }

  /**
   * Send a password reset email
   * @param to Recipient email
   * @param token Reset token
   * @param username User's name
   * @returns Promise resolving to success status
   */
  async sendPasswordResetEmail(
    to: string,
    token: string,
    username: string
  ): Promise<boolean> {
    const appUrl = this.configService.get<string>(
      "APP_URL",
      "http://localhost:3000"
    );
    const resetLink = `${appUrl}/reset-password?token=${token}`;

    const subject = "Password Reset Request";
    const html = `
      <h1>Password Reset</h1>
      <p>Hello ${username},</p>
      <p>We received a request to reset your password. If you didn't make this request, you can ignore this email.</p>
      <p>To reset your password, click the link below:</p>
      <p><a href="${resetLink}" style="padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
      <p>Or copy and paste this URL into your browser:</p>
      <p>${resetLink}</p>
      <p>This link will expire in 1 hour.</p>
      <p>Thank you,</p>
      <p>The Job Platform Team</p>
    `;

    const text = `
      Password Reset

      Hello ${username},

      We received a request to reset your password. If you didn't make this request, you can ignore this email.

      To reset your password, visit this link:
      ${resetLink}

      This link will expire in 1 hour.

      Thank you,
      The Job Platform Team
    `;

    return this.sendEmail({
      to,
      subject,
      html,
      text,
    });
  }

  /**
   * Send a welcome email to a new user
   * @param to Recipient email
   * @param username User's name
   * @returns Promise resolving to success status
   */
  async sendWelcomeEmail(to: string, username: string): Promise<boolean> {
    const appUrl = this.configService.get<string>(
      "APP_URL",
      "http://localhost:3000"
    );

    const subject = "Welcome to Job Platform";
    const html = `
      <h1>Welcome to Job Platform!</h1>
      <p>Hello ${username},</p>
      <p>Thank you for joining our platform. We're excited to have you on board!</p>
      <p>To get started, visit our platform and complete your profile:</p>
      <p><a href="${appUrl}" style="padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Visit Job Platform</a></p>
      <p>If you have any questions, feel free to contact our support team.</p>
      <p>Best regards,</p>
      <p>The Job Platform Team</p>
    `;

    const text = `
      Welcome to Job Platform!

      Hello ${username},

      Thank you for joining our platform. We're excited to have you on board!

      To get started, visit our platform and complete your profile:
      ${appUrl}

      If you have any questions, feel free to contact our support team.

      Best regards,
      The Job Platform Team
    `;

    return this.sendEmail({
      to,
      subject,
      html,
      text,
    });
  }
}
