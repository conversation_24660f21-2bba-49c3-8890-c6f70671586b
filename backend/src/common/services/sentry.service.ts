import { Injectable, OnModuleInit } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import Sentry from "@sentry/node";
import { ProfilingIntegration } from "@sentry/profiling-node";

@Injectable()
export class SentryService implements OnModuleInit {
  constructor(private configService: ConfigService) {}

  onModuleInit() {
    const sentryDsn = this.configService.get<string>("SENTRY_DSN");
    const nodeEnv = this.configService.get<string>("NODE_ENV", "development");

    if (sentryDsn && nodeEnv === "production") {
      Sentry.init({
        dsn: sentryDsn,
        environment: nodeEnv,
        integrations: [new ProfilingIntegration()],
        // Performance Monitoring
        tracesSampleRate: 0.1,
        // Profiling
        profilesSampleRate: 0.1,
        // Release tracking
        ...(process.env.npm_package_version && {
          release: process.env.npm_package_version,
        }),
        beforeSend(event) {
          // Filter out sensitive information
          if (event.request?.headers) {
            delete event.request.headers.authorization;
            delete event.request.headers.cookie;
          }
          return event;
        },
      });
    }
  }

  captureException(error: Error, context?: Record<string, any>) {
    if (context) {
      Sentry.withScope((scope) => {
        Object.keys(context).forEach((key) => {
          scope.setTag(key, context[key]);
        });
        Sentry.captureException(error);
      });
    } else {
      Sentry.captureException(error);
    }
  }

  captureMessage(
    message: string,
    level: Sentry.SeverityLevel = "info",
    context?: Record<string, any>
  ) {
    if (context) {
      Sentry.withScope((scope) => {
        Object.keys(context).forEach((key) => {
          scope.setTag(key, context[key]);
        });
        Sentry.captureMessage(message, level);
      });
    } else {
      Sentry.captureMessage(message, level);
    }
  }

  setUser(user: { id: string; email?: string; role?: string }) {
    Sentry.setUser(user);
  }

  addBreadcrumb(breadcrumb: Sentry.Breadcrumb) {
    Sentry.addBreadcrumb(breadcrumb);
  }

  startTransaction(name: string, op: string) {
    return Sentry.startTransaction({ name, op });
  }
}
