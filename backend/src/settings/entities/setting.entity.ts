import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON>olumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity("settings")
export class Setting {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column({ unique: true })
  key!: string;

  @Column("text")
  value!: string;

  @Column({ nullable: true })
  description!: string;

  @Column({ default: "string" })
  type!: string; // string, number, boolean, json

  @Column({ default: false })
  isSystem!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
