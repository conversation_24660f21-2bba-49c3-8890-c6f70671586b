import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Inject,
} from "@nestjs/common";
import { JobTemplatesService } from "./job-templates.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { CreateJobTemplateDto } from "@shared/validation";
import { UserRole } from "src/common/enums/user-role.enum";

@Controller("job-templates")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.COMPANY)
export class JobTemplatesController {
  constructor(
    @Inject(JobTemplatesService)
    private readonly jobTemplatesService: JobTemplatesService
  ) {}

  @Post()
  create(
    @Body() createJobTemplateDto: CreateJobTemplateDto,
    @Request() req: any
  ) {
    return this.jobTemplatesService.create(
      createJobTemplateDto,
      req.user.userId
    );
  }

  @Get()
  findAll(@Request() req: any) {
    return this.jobTemplatesService.findAll(req.user.userId);
  }

  @Get(":id")
  findOne(@Param("id") id: string, @Request() req: any) {
    return this.jobTemplatesService.findOne(id, req.user.userId);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateJobTemplateDto: Partial<CreateJobTemplateDto>,
    @Request() req: any
  ) {
    return this.jobTemplatesService.update(
      id,
      updateJobTemplateDto,
      req.user.userId
    );
  }

  @Delete(":id")
  remove(@Param("id") id: string, @Request() req: any) {
    return this.jobTemplatesService.remove(id, req.user.userId);
  }
}
