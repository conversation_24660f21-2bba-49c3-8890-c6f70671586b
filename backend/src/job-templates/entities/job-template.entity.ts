import { User } from "src/users/entities/user.entity";
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from "typeorm";

@Entity("job_templates")
export class JobTemplate {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column()
  title!: string;

  @Column("text")
  description!: string;

  @Column("int")
  duration!: number; // in hours

  @Column("int")
  trustScoreRequired!: number;

  @Column("decimal", { precision: 10, scale: 2 })
  paymentAmount!: number;

  @Column({ default: false })
  requiresLaptop!: boolean;

  @Column({ default: false })
  requiresSmartphone!: boolean;

  @Column({ default: false })
  isEmergencyJob!: boolean;

  @ManyToOne(() => User)
  @JoinColumn({ name: "postedBy" })
  postedByUser!: User;

  @Column()
  postedBy!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
