import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { ActivityLogService } from "../activity-log/activity-log.service";
import { NotificationsService } from "../notifications/notifications.service";
import { TrustScoreService } from "../users/trust-score.service";
import { EmailService } from "../common/services/email/email.service";
import { ApplicationStatus, UserRole } from "@shared/types";
import { CreateApplicationDto } from "@shared/validation";
import { UpdateApplicationDto } from "./dto/update-application.dto";
import { Job } from "src/jobs/entities/job.entity";
import { User } from "src/users/entities/user.entity";

import { Application } from "./entities/application.entity";
import { JobStatus } from "src/common/enums/job-status.enum";

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(Application)
    private applicationsRepository: Repository<Application>,
    @InjectRepository(Job)
    private jobsRepository: Repository<Job>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @Inject(ActivityLogService)
    private activityLogService: ActivityLogService,
    @Inject(NotificationsService)
    private notificationsService: NotificationsService,
    @Inject(TrustScoreService)
    private trustScoreService: TrustScoreService,
    @Inject(EmailService)
    private emailService: EmailService
  ) {}

  async apply(
    workerId: string,
    jobId: string,
    coverLetter?: string
  ): Promise<Application> {
    // Verify worker exists and is a worker
    const worker = await this.usersRepository.findOne({
      where: { id: workerId },
    });
    if (!worker) {
      throw new NotFoundException(`Worker with ID ${workerId} not found`);
    }
    if (worker.role !== UserRole.WORKER) {
      throw new BadRequestException("Only workers can apply for jobs");
    }

    // Verify job exists and is open
    const job = await this.jobsRepository.findOne({
      where: { id: jobId },
      relations: ["company"],
    });
    if (!job) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
    if (job.status !== JobStatus.OPEN) {
      throw new BadRequestException("Cannot apply to a job that is not open");
    }

    // Check if worker meets trust score requirement
    if (!job.isEmergencyJob && worker.trustScore < job.trustScoreRequired) {
      throw new BadRequestException("Your trust score is too low for this job");
    }

    // Check if worker has already applied
    const existingApplication = await this.applicationsRepository.findOne({
      where: { workerId, jobId },
    });
    if (existingApplication) {
      throw new BadRequestException("You have already applied for this job");
    }

    // Create application
    const application = this.applicationsRepository.create({
      workerId,
      jobId,
      coverLetter: coverLetter || "",
      isEmergencyJob: job.isEmergencyJob,
    });

    const savedApplication = (await this.applicationsRepository.save(
      application
    )) as Application;

    // Notify company via in-app notification
    await this.notificationsService.create({
      userId: job.companyId,
      title: "New Job Application",
      message: `${worker.name} has applied for your job: ${job.title}`,
      type: "application",
      metadata: { applicationId: savedApplication.id, jobId, workerId },
      link: `/jobs/${jobId}/applications`,
    });

    // Send email notification to company if they have an email
    if (job?.company?.email) {
      try {
        await this.emailService.sendJobApplicationEmail(
          job.company.email,
          job.company.name,
          job.title,
          worker.name,
          savedApplication.id
        );
      } catch (error) {
        // Log error but don't fail the operation
        console.error(
          `Failed to send job application email: ${(error as Error).message}`
        );
      }
    }

    return savedApplication;
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    filters?: {
      workerId?: string;
      jobId?: string;
      status?: ApplicationStatus;
    }
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
  }> {
    const queryBuilder = this.applicationsRepository
      .createQueryBuilder("application")
      .leftJoinAndSelect("application.worker", "worker")
      .leftJoinAndSelect("application.job", "job")
      .leftJoinAndSelect("job.company", "company")
      .orderBy("application.createdAt", "DESC");

    if (filters) {
      if (filters.workerId) {
        queryBuilder.andWhere("application.workerId = :workerId", {
          workerId: filters.workerId,
        });
      }
      if (filters.jobId) {
        queryBuilder.andWhere("application.jobId = :jobId", {
          jobId: filters.jobId,
        });
      }
      if (filters.status) {
        queryBuilder.andWhere("application.status = :status", {
          status: filters.status,
        });
      }
    }

    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      data,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<Application> {
    const application = await this.applicationsRepository.findOne({
      where: { id },
      relations: ["worker", "job", "job.company"],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  /**
   * Find an application by ID with role-based permission checking
   * @param id The application ID
   * @param userId The ID of the user making the request
   * @param userRole The role of the user making the request
   * @returns The application if the user has permission to view it
   */
  async findOneWithPermissionCheck(
    id: string,
    userId: string,
    userRole: UserRole
  ): Promise<Application> {
    const application = await this.findOne(id);

    // Check permissions
    if (userRole === UserRole.WORKER && application.workerId !== userId) {
      throw new BadRequestException("You can only view your own applications");
    }

    if (userRole === UserRole.COMPANY && application.job.companyId !== userId) {
      throw new BadRequestException(
        "You can only view applications for your own jobs"
      );
    }

    return application;
  }

  async acceptApplication(
    applicationId: string,
    companyId: string
  ): Promise<Application> {
    const application = await this.findOne(applicationId);

    // Verify company owns this job
    if (application.job.companyId !== companyId) {
      throw new BadRequestException(
        "You can only accept applications for your own jobs"
      );
    }

    // Verify application is pending
    if (application.status !== ApplicationStatus.PENDING) {
      throw new BadRequestException("Can only accept pending applications");
    }

    // Update application status
    application.status = ApplicationStatus.ACCEPTED;
    const acceptedApplication = await this.applicationsRepository.save(
      application
    );

    // Update job status to in-progress if it was open
    if (application.job.status === JobStatus.OPEN) {
      application.job.status = JobStatus.IN_PROGRESS;
      await this.jobsRepository.save(application.job);
    }

    // Notify worker
    await this.notificationsService.create({
      userId: application.workerId,
      title: "Application Accepted",
      message: `Your application for "${application.job.title}" has been accepted`,
      type: "application",
      metadata: { applicationId, jobId: application.jobId },
      link: `/applications/${applicationId}`,
    });

    return acceptedApplication;
  }

  async rejectApplication(
    applicationId: string,
    companyId: string,
    reason: string
  ): Promise<Application> {
    const application = await this.findOne(applicationId);

    // Verify company owns this job
    if (application.job.companyId !== companyId) {
      throw new BadRequestException(
        "You can only reject applications for your own jobs"
      );
    }

    // Verify application is pending
    if (application.status !== ApplicationStatus.PENDING) {
      throw new BadRequestException("Can only reject pending applications");
    }

    // Update application status
    application.status = ApplicationStatus.REJECTED;
    application.rejectionReason = reason;
    const rejectedApplication = await this.applicationsRepository.save(
      application
    );

    // Notify worker
    await this.notificationsService.create({
      userId: application.workerId,
      title: "Application Rejected",
      message: `Your application for "${application.job.title}" has been rejected. Reason: ${reason}`,
      type: "application",
      metadata: { applicationId, jobId: application.jobId },
      link: `/applications/${applicationId}`,
    });

    return rejectedApplication;
  }

  async cancelApplication(
    applicationId: string,
    workerId: string,
    reason: string
  ): Promise<Application> {
    const application = await this.findOne(applicationId);

    // Verify worker owns this application
    if (application.workerId !== workerId) {
      throw new BadRequestException(
        "You can only cancel your own applications"
      );
    }

    // Verify application is pending or accepted
    if (
      ![ApplicationStatus.PENDING, ApplicationStatus.ACCEPTED].includes(
        application.status
      )
    ) {
      throw new BadRequestException(
        "Can only cancel pending or accepted applications"
      );
    }

    const previousStatus = application.status;

    // Update application status
    application.status = ApplicationStatus.CANCELLED;
    application.cancellationReason = reason;
    const cancelledApplication = await this.applicationsRepository.save(
      application
    );

    // If cancelling an accepted application, update trust score negatively
    if (previousStatus === ApplicationStatus.ACCEPTED) {
      // Calculate days until job starts
      const now = new Date();
      const jobStart = new Date(application.job.startDateTime);
      const daysUntilJob = Math.ceil(
        (jobStart.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Apply different penalties based on how close to job start date
      let trustScoreChange = 0;
      let reason = "";

      if (daysUntilJob <= 1) {
        trustScoreChange = -25;
        reason = "Last-minute job cancellation (within 24 hours)";
      } else if (daysUntilJob <= 3) {
        trustScoreChange = -15;
        reason = "Late job cancellation (within 3 days)";
      } else {
        trustScoreChange = -5;
        reason = "Job cancellation";
      }

      await this.trustScoreService.updateTrustScore({
        userId: workerId,
        change: trustScoreChange,
        reason,
        relatedEntityType: "application",
        relatedEntityId: applicationId,
      });
    }

    // Notify company
    await this.notificationsService.create({
      userId: application.job.companyId,
      title: "Application Cancelled",
      message: `${application.worker.name} has cancelled their application for "${application.job.title}". Reason: ${reason}`,
      type: "application",
      metadata: { applicationId, jobId: application.jobId, workerId },
      link: `/jobs/${application.jobId}/applications`,
    });

    return cancelledApplication;
  }

  async markNoShow(
    applicationId: string,
    companyId: string
  ): Promise<Application> {
    const application = await this.findOne(applicationId);

    // Verify company owns this job
    if (application.job.companyId !== companyId) {
      throw new BadRequestException(
        "You can only mark no-shows for your own jobs"
      );
    }

    // Verify application is accepted
    if (application.status !== ApplicationStatus.ACCEPTED) {
      throw new BadRequestException(
        "Can only mark accepted applications as no-show"
      );
    }

    // Update application status
    application.status = ApplicationStatus.NO_SHOW;
    const noShowApplication = await this.applicationsRepository.save(
      application
    );

    // Update worker's trust score negatively
    await this.trustScoreService.updateTrustScore({
      userId: application.workerId,
      change: -25,
      reason: "No-show for accepted job",
      relatedEntityType: "application",
      relatedEntityId: applicationId,
    });

    // Notify worker
    await this.notificationsService.create({
      userId: application.workerId,
      title: "Marked as No-Show",
      message: `You have been marked as a no-show for "${application.job.title}". This will negatively affect your trust score.`,
      type: "application",
      metadata: { applicationId, jobId: application.jobId },
      link: `/applications/${applicationId}`,
    });

    return noShowApplication;
  }

  async markCompleted(
    applicationId: string,
    companyId: string
  ): Promise<Application> {
    const application = await this.findOne(applicationId);

    // Verify company owns this job
    if (application.job.companyId !== companyId) {
      throw new BadRequestException(
        "You can only mark completions for your own jobs"
      );
    }

    // Verify application is accepted
    if (application.status !== ApplicationStatus.ACCEPTED) {
      throw new BadRequestException(
        "Can only mark accepted applications as completed"
      );
    }

    // Update application status
    application.status = ApplicationStatus.COMPLETED;
    const completedApplication = await this.applicationsRepository.save(
      application
    );

    // Update worker's trust score positively
    const trustScoreChange = application.isEmergencyJob ? 10 : 5;
    const reason = application.isEmergencyJob
      ? "Completed emergency job"
      : "Completed job";

    await this.trustScoreService.updateTrustScore({
      userId: application.workerId,
      change: trustScoreChange,
      reason,
      relatedEntityType: "application",
      relatedEntityId: applicationId,
    });

    // Notify worker
    await this.notificationsService.create({
      userId: application.workerId,
      title: "Job Completed",
      message: `Your work for "${application.job.title}" has been marked as completed. Your trust score has increased.`,
      type: "application",
      metadata: { applicationId, jobId: application.jobId },
      link: `/applications/${applicationId}`,
    });

    return completedApplication;
  }

  /**
   * Find applications with role-based filtering
   * @param userId The ID of the user making the request
   * @param userRole The role of the user making the request
   * @param page The page number
   * @param limit The number of items per page
   * @param filters Additional filters
   * @returns Paginated applications with role-based filtering
   */
  async findAllWithRoleFiltering(
    userId: string,
    userRole: UserRole,
    page: number = 1,
    limit: number = 10,
    filters?: {
      workerId?: string;
      jobId?: string;
      status?: ApplicationStatus;
    }
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
  }> {
    // Create a new filters object to avoid modifying the original
    const roleFilters = { ...filters };

    // If user is a worker, they can only see their own applications
    if (userRole === UserRole.WORKER) {
      roleFilters.workerId = userId;
      return this.findAll(page, limit, roleFilters);
    }

    // If user is a company, they can only see applications for their own jobs
    if (userRole === UserRole.COMPANY) {
      if (roleFilters.jobId) {
        // If a specific job ID is provided, verify it belongs to the company
        const job = await this.jobsRepository.findOne({
          where: { id: roleFilters.jobId, companyId: userId },
        });

        if (!job) {
          return {
            data: [],
            total: 0,
            page,
            limit,
          };
        }

        return this.findAll(page, limit, roleFilters);
      } else {
        // If no job ID is provided, we need to filter applications for the company's jobs
        const applications = await this.findAll(page, limit, roleFilters);

        // Filter applications for company's jobs
        const filteredApplications = applications.data.filter(
          (app) => app.job.companyId === userId
        );

        return {
          data: filteredApplications,
          total: filteredApplications.length,
          page,
          limit,
        };
      }
    }

    // For admins, return all applications with the provided filters
    return this.findAll(page, limit, roleFilters);
  }

  async getWorkerApplicationStats(workerId: string): Promise<{
    total: number;
    pending: number;
    accepted: number;
    completed: number;
    rejected: number;
    cancelled: number;
    noShow: number;
  }> {
    const stats = {
      total: 0,
      pending: 0,
      accepted: 0,
      completed: 0,
      rejected: 0,
      cancelled: 0,
      noShow: 0,
    };

    const applications = await this.applicationsRepository.find({
      where: { workerId },
    });

    stats.total = applications.length;

    for (const app of applications) {
      switch (app.status) {
        case ApplicationStatus.PENDING:
          stats.pending++;
          break;
        case ApplicationStatus.ACCEPTED:
          stats.accepted++;
          break;
        case ApplicationStatus.COMPLETED:
          stats.completed++;
          break;
        case ApplicationStatus.REJECTED:
          stats.rejected++;
          break;
        case ApplicationStatus.CANCELLED:
          stats.cancelled++;
          break;
        case ApplicationStatus.NO_SHOW:
          stats.noShow++;
          break;
      }
    }

    return stats;
  }

  /**
   * Count applications with optional filters
   * @param filters Optional filters to apply
   * @returns The count of applications matching the filters
   */
  async count(filters?: {
    workerId?: string;
    jobId?: string;
    status?: ApplicationStatus;
    createdAfter?: Date;
    createdBefore?: Date;
  }): Promise<number> {
    const queryBuilder =
      this.applicationsRepository.createQueryBuilder("application");

    if (filters) {
      if (filters.workerId) {
        queryBuilder.andWhere("application.workerId = :workerId", {
          workerId: filters.workerId,
        });
      }
      if (filters.jobId) {
        queryBuilder.andWhere("application.jobId = :jobId", {
          jobId: filters.jobId,
        });
      }
      if (filters.status) {
        queryBuilder.andWhere("application.status = :status", {
          status: filters.status,
        });
      }
      if (filters.createdAfter) {
        queryBuilder.andWhere("application.createdAt >= :createdAfter", {
          createdAfter: filters.createdAfter,
        });
      }
      if (filters.createdBefore) {
        queryBuilder.andWhere("application.createdAt <= :createdBefore", {
          createdBefore: filters.createdBefore,
        });
      }
    }

    return queryBuilder.getCount();
  }

  /**
   * Create a new application
   * @param workerId ID of the worker applying for the job
   * @param createApplicationDto Data for creating the application
   * @returns The created application
   */
  async create(
    workerId: string,
    createApplicationDto: CreateApplicationDto
  ): Promise<Application> {
    const { jobId, coverLetter } = createApplicationDto;

    // Verify worker exists and is a worker
    const worker = await this.usersRepository.findOne({
      where: { id: workerId },
    });
    if (!worker) {
      throw new NotFoundException(`Worker with ID ${workerId} not found`);
    }
    if (worker.role !== UserRole.WORKER) {
      throw new BadRequestException("Only workers can apply for jobs");
    }

    // Verify job exists and is open
    const job = await this.jobsRepository.findOne({
      where: { id: jobId },
      relations: ["company"],
    });
    if (!job) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
    if (job.status !== JobStatus.OPEN) {
      throw new BadRequestException("Cannot apply to a job that is not open");
    }

    // Check if worker meets trust score requirement
    if (!job.isEmergencyJob && worker.trustScore < job.trustScoreRequired) {
      throw new BadRequestException("Your trust score is too low for this job");
    }

    // Check if worker has already applied
    const existingApplication = await this.applicationsRepository.findOne({
      where: { workerId, jobId },
    });
    if (existingApplication) {
      throw new BadRequestException("You have already applied for this job");
    }

    // Create application
    const application = this.applicationsRepository.create({
      workerId,
      jobId,
      coverLetter: coverLetter || "",
      isEmergencyJob: job.isEmergencyJob,
      status: ApplicationStatus.PENDING,
    });

    const savedApplication = (await this.applicationsRepository.save(
      application
    )) as Application;

    // Log activity
    await this.activityLogService.logActivity({
      userId: workerId,
      action: "application_created",
      description: `Applied for job: ${job.title}`,
      entityId: savedApplication.id,
      entityType: "application",
      metadata: { jobId },
    });

    // Notify company via in-app notification
    await this.notificationsService.create({
      userId: job.companyId,
      title: "New Job Application",
      message: `${worker.name} has applied for your job: ${job.title}`,
      type: "application",
      metadata: { applicationId: savedApplication.id, jobId, workerId },
      link: `/jobs/${jobId}/applications`,
    });

    // Send email notification to company if they have an email
    if (job?.company?.email) {
      try {
        await this.emailService.sendJobApplicationEmail(
          job.company.email,
          job.company.name,
          job.title,
          worker.name,
          savedApplication.id
        );
      } catch (error) {
        // Log error but don't fail the operation
        console.error(
          `Failed to send job application email: ${(error as Error).message}`
        );
      }
    }

    return savedApplication;
  }

  /**
   * Update an existing application
   * @param id ID of the application to update
   * @param userId ID of the user making the update (worker or company)
   * @param updateApplicationDto Data for updating the application
   * @returns The updated application
   */
  async update(
    id: string,
    userId: string,
    updateApplicationDto: UpdateApplicationDto
  ): Promise<Application> {
    const application = await this.findOne(id);
    const user = await this.usersRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Check permissions based on user role
    if (user.role === UserRole.WORKER) {
      // Workers can only update their own applications
      if (application.workerId !== userId) {
        throw new BadRequestException(
          "You can only update your own applications"
        );
      }

      // Workers can only update pending applications
      if (application.status !== ApplicationStatus.PENDING) {
        throw new BadRequestException(
          "You can only update pending applications"
        );
      }

      // Workers can only update coverLetter
      const allowedFields = ["coverLetter"];
      const providedFields = Object.keys(updateApplicationDto);

      const invalidFields = providedFields.filter(
        (field) => !allowedFields.includes(field)
      );
      if (invalidFields.length > 0) {
        throw new BadRequestException(
          `Workers can only update: ${allowedFields.join(", ")}`
        );
      }
    } else if (user.role === UserRole.COMPANY) {
      // Companies can only update applications for their own jobs
      if (application.job.companyId !== userId) {
        throw new BadRequestException(
          "You can only update applications for your own jobs"
        );
      }

      // Companies can update notes and internal fields
      const allowedFields = ["notes", "internalRating"];
      const providedFields = Object.keys(updateApplicationDto);

      const invalidFields = providedFields.filter(
        (field) => !allowedFields.includes(field)
      );
      if (invalidFields.length > 0) {
        throw new BadRequestException(
          `Companies can only update: ${allowedFields.join(", ")}`
        );
      }
    } else if (user.role === UserRole.ADMIN) {
      // Admins can update any field
      // No additional validation needed
    } else {
      throw new BadRequestException(
        "You don't have permission to update applications"
      );
    }

    // Apply updates
    Object.assign(application, updateApplicationDto);

    // Save and return updated application
    const updatedApplication = await this.applicationsRepository.save(
      application
    );

    // Log activity
    await this.activityLogService.logActivity({
      userId,
      action: "application_updated",
      description: `Updated application for job: ${application.job.title}`,
      entityId: id,
      entityType: "application",
      metadata: {
        jobId: application.jobId,
        updatedFields: Object.keys(updateApplicationDto),
      },
    });

    return updatedApplication;
  }
}
