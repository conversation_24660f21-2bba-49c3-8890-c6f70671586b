import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsOptional, Length, IsUUID } from "class-validator";

export class CreateApplicationDto {
  @ApiProperty({
    description: "Job ID to apply for",
    example: "550e8400-e29b-41d4-a716-************",
  })
  @IsUUID()
  jobId!: string;

  @ApiPropertyOptional({
    description: "Cover letter or application message",
    example:
      "I am very interested in this job opportunity and believe my experience in inventory management makes me a strong candidate.",
  })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  coverLetter?: string;
}
