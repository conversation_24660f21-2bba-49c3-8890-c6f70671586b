import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ApplicationsService } from "./applications.service";
import { ApplicationsController } from "./applications.controller";
import { Application } from "./entities/application.entity";

import { UsersModule } from "../users/users.module";
import { ActivityLogModule } from "../activity-log/activity-log.module";
import { NotificationsModule } from "../notifications/notifications.module";
import { Job } from "src/jobs/entities/job.entity";
import { User } from "src/users/entities/user.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([Application, Job, User]),
    ActivityLogModule,
    NotificationsModule,
    UsersModule,
  ],
  providers: [ApplicationsService],
  controllers: [ApplicationsController],
  exports: [ApplicationsService],
})
export class ApplicationsModule {}
