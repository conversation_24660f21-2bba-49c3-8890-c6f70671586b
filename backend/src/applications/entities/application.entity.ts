import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";
import { User } from "../../users/entities/user.entity";
import { Job } from "../../jobs/entities/job.entity";
import { ApplicationStatus } from "@shared/types";

@Entity("applications")
export class Application {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column()
  @Index()
  workerId!: string;

  @ManyToOne(() => User, (user) => user.applications)
  @JoinColumn({ name: "workerId" })
  worker!: User;

  @Column()
  @Index()
  jobId!: string;

  @ManyToOne(() => Job, (job) => job.applications)
  @JoinColumn({ name: "jobId" })
  job!: Job;

  @Column({
    type: "enum",
    enum: ApplicationStatus,
    default: ApplicationStatus.PENDING,
  })
  status!: ApplicationStatus;

  @Column({ type: "text", nullable: true })
  coverLetter?: string;

  @Column({ default: false })
  isEmergencyJob!: boolean;

  @Column({ nullable: true })
  rejectionReason?: string;

  @Column({ nullable: true })
  cancellationReason?: string;

  @Column({ nullable: true })
  cancelledAt?: Date;

  @Column({ nullable: true })
  completedAt?: Date;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
