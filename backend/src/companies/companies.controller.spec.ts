import { Test, TestingModule } from "@nestjs/testing";
import { CompaniesController } from "./companies.controller";
import { CompaniesService } from "./companies.service";
import { CreateCompanyDto } from "./dto/create-company.dto";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { UpdateCompanyKycDto } from "./dto/update-company-kyc.dto";
import { UserRole } from "../common/enums/user-role.enum";
import { DocumentType } from "../common/enums/document-type.enum";

import { BadRequestException } from "@nestjs/common";

const mockCompaniesService = () => ({
  create: jest.fn(),
  findAll: jest.fn(),
  findOne: jest.fn(),
  findByUserId: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  updateKyc: jest.fn(),
  uploadDocument: jest.fn(),
  verifyDocument: jest.fn(),
});

describe("CompaniesController", () => {
  let controller: CompaniesController;
  let service: CompaniesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CompaniesController],
      providers: [
        {
          provide: CompaniesService,
          useFactory: mockCompaniesService,
        },
      ],
    }).compile();

    controller = module.get<CompaniesController>(CompaniesController);
    service = module.get<CompaniesService>(CompaniesService);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  describe("create", () => {
    it("should create a company", async () => {
      const userId = "test-user-id";
      const createCompanyDto: CreateCompanyDto = {
        name: "Test Company",
        industry: "Technology",
      };
      const company = { id: "test-company-id", ...createCompanyDto, userId };

      jest.spyOn(service, "create").mockResolvedValue(company as any);

      const result = await controller.create(
        { user: { id: userId } } as any,
        createCompanyDto
      );

      expect(service.create).toHaveBeenCalledWith(userId, createCompanyDto);
      expect(result).toEqual(company);
    });
  });

  describe("findAll", () => {
    it("should return paginated companies", async () => {
      const queryCompaniesDto = { page: 1, limit: 10 };
      const paginatedResult = {
        data: [{ id: "test-company-id", name: "Test Company" }],
        meta: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      };

      jest.spyOn(service, "findAll").mockResolvedValue(paginatedResult as any);

      const result = await controller.findAll(queryCompaniesDto);

      expect(service.findAll).toHaveBeenCalledWith(queryCompaniesDto);
      expect(result).toEqual(paginatedResult);
    });
  });

  describe("findProfile", () => {
    it("should return company profile for authenticated user", async () => {
      const userId = "test-user-id";
      const company = { id: "test-company-id", name: "Test Company", userId };

      jest.spyOn(service, "findByUserId").mockResolvedValue(company as any);

      const result = await controller.findProfile({
        user: { id: userId },
      } as any);

      expect(service.findByUserId).toHaveBeenCalledWith(userId);
      expect(result).toEqual(company);
    });
  });

  describe("findOne", () => {
    it("should return a company by id", async () => {
      const companyId = "test-company-id";
      const company = { id: companyId, name: "Test Company" };

      jest.spyOn(service, "findOne").mockResolvedValue(company as any);

      const result = await controller.findOne(companyId);

      expect(service.findOne).toHaveBeenCalledWith(companyId);
      expect(result).toEqual(company);
    });
  });

  describe("update", () => {
    it("should update a company", async () => {
      const companyId = "test-company-id";
      const userId = "test-user-id";
      const updateCompanyDto: UpdateCompanyDto = { name: "Updated Company" };
      const company = { id: companyId, name: "Updated Company", userId };

      jest
        .spyOn(service, "findByUserId")
        .mockResolvedValue({ id: companyId } as any);
      jest.spyOn(service, "update").mockResolvedValue(company as any);

      const result = await controller.update(companyId, updateCompanyDto, {
        user: { id: userId, role: UserRole.COMPANY },
      } as any);

      expect(service.findByUserId).toHaveBeenCalledWith(userId);
      expect(service.update).toHaveBeenCalledWith(companyId, updateCompanyDto);
      expect(result).toEqual(company);
    });

    it("should throw BadRequestException if company does not belong to user", async () => {
      const companyId = "test-company-id";
      const userId = "test-user-id";
      const updateCompanyDto: UpdateCompanyDto = { name: "Updated Company" };

      jest
        .spyOn(service, "findByUserId")
        .mockResolvedValue({ id: "different-company-id" } as any);

      await expect(
        controller.update(companyId, updateCompanyDto, {
          user: { id: userId, role: UserRole.COMPANY },
        } as any)
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("updateKyc", () => {
    it("should update company KYC information", async () => {
      const userId = "test-user-id";
      const companyId = "test-company-id";
      const updateCompanyKycDto: UpdateCompanyKycDto = {
        registrationNumber: "REG123",
        taxId: "TAX123",
      };
      const company = {
        id: companyId,
        registrationNumber: "REG123",
        taxId: "TAX123",
        userId,
      };

      jest
        .spyOn(service, "findByUserId")
        .mockResolvedValue({ id: companyId } as any);
      jest.spyOn(service, "updateKyc").mockResolvedValue(company as any);

      const result = await controller.updateKyc(
        { user: { id: userId } } as any,
        updateCompanyKycDto
      );

      expect(service.findByUserId).toHaveBeenCalledWith(userId);
      expect(service.updateKyc).toHaveBeenCalledWith(
        companyId,
        updateCompanyKycDto
      );
      expect(result).toEqual(company);
    });
  });

  describe("uploadDocument", () => {
    it("should upload a document for a company", async () => {
      const userId = "test-user-id";
      const companyId = "test-company-id";
      const documentType = DocumentType.COMPANY_REGISTRATION;
      const documentNumber = "DOC123";
      const file = { originalname: "document.pdf" };
      const document = {
        id: "test-document-id",
        companyId,
        documentType,
        documentUrl: "https://example.com/documents/document.pdf",
        documentNumber,
      };

      jest
        .spyOn(service, "findByUserId")
        .mockResolvedValue({ id: companyId } as any);
      jest.spyOn(service, "uploadDocument").mockResolvedValue(document as any);

      const result = await controller.uploadDocument(
        { user: { id: userId } } as any,
        file as any,
        documentType,
        documentNumber
      );

      expect(service.findByUserId).toHaveBeenCalledWith(userId);
      expect(service.uploadDocument).toHaveBeenCalledWith(
        companyId,
        documentType,
        expect.any(String),
        documentNumber
      );
      expect(result).toEqual(document);
    });

    it("should throw BadRequestException if file is missing", async () => {
      const userId = "test-user-id";
      const documentType = DocumentType.COMPANY_REGISTRATION;

      await expect(
        controller.uploadDocument(
          { user: { id: userId } } as any,
          undefined as any,
          documentType
        )
      ).rejects.toThrow(BadRequestException);
    });
  });
});
